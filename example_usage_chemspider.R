# ChemSpider 集成使用示例
# 
# 此脚本展示如何使用增强后的 pubchem.R 函数，
# 该函数现在支持在 PubChem 查询失败时自动尝试 ChemSpider 数据库

# 加载函数
source("pubchem.R")

# 使用示例
example_usage <- function() {
  cat("=== ChemSpider 集成功能使用示例 ===\n\n")
  
  # 假设您有一个包含化合物名称的Excel文件
  input_file <- "your_compounds.xlsx"  # 替换为您的实际文件路径
  
  # 基本使用 - 现在会自动尝试 PubChem 和 ChemSpider
  cat("1. 基本使用（自动查询 PubChem + ChemSpider）:\n")
  cat("Add_pubchem_info_pro('", input_file, "')\n\n")
  
  # 自定义输出文件
  cat("2. 指定输出文件:\n")
  cat("Add_pubchem_info_pro(\n")
  cat("  file_path = '", input_file, "',\n")
  cat("  output_path = 'results_with_chemspider.xlsx'\n")
  cat(")\n\n")
  
  # 调整查询延迟（推荐用于大量查询）
  cat("3. 调整查询延迟（避免被限制访问）:\n")
  cat("Add_pubchem_info_pro(\n")
  cat("  file_path = '", input_file, "',\n")
  cat("  delay = 2  # 每次查询间隔2秒\n")
  cat(")\n\n")
  
  # 强制查询所有化合物
  cat("4. 强制重新查询所有化合物:\n")
  cat("Add_pubchem_info_pro(\n")
  cat("  file_path = '", input_file, "',\n")
  cat("  force_query_all = TRUE\n")
  cat(")\n\n")
  
  # 只查询特定字段
  cat("5. 只查询特定字段:\n")
  cat("Add_pubchem_info_pro(\n")
  cat("  file_path = '", input_file, "',\n")
  cat("  fields_to_update = c('SMILES', 'InChIKey')\n")
  cat(")\n\n")
}

# 查询流程说明
explain_workflow <- function() {
  cat("=== 查询流程说明 ===\n\n")
  
  cat("增强后的查询流程:\n")
  cat("1. 首先尝试通过化合物名称在 PubChem 中查询\n")
  cat("2. 如果失败且存在 InChIKey，尝试通过 InChIKey 在 PubChem 中查询\n")
  cat("3. 如果 PubChem 查询都失败，尝试通过化合物名称在 ChemSpider 中查询\n")
  cat("4. 如果失败且存在 InChIKey，尝试通过 InChIKey 在 ChemSpider 中查询\n")
  cat("5. 返回找到的任何信息\n\n")
  
  cat("支持的数据库:\n")
  cat("✅ PubChem (主要数据库)\n")
  cat("✅ ChemSpider (RSC) (备用数据库)\n\n")
  
  cat("支持的查询字段:\n")
  cat("• SMILES - 分子结构表示\n")
  cat("• MolecularFormula - 分子式\n")
  cat("• MolecularWeight - 分子量\n")
  cat("• InChIKey - 国际化学标识符键\n\n")
}

# 注意事项
show_notes <- function() {
  cat("=== 重要注意事项 ===\n\n")
  
  cat("⚠️  网络和API限制:\n")
  cat("• ChemSpider 查询可能较慢，建议设置较长的延迟时间\n")
  cat("• 某些网络环境可能无法访问 ChemSpider\n")
  cat("• 大量查询时建议分批处理\n\n")
  
  cat("📝 输入文件要求:\n")
  cat("• Excel 文件必须包含 'NAME' 列（化合物名称）\n")
  cat("• 可选包含 'InChIKey' 列用于备用查询\n")
  cat("• 支持 .xlsx 格式\n\n")
  
  cat("🔧 性能优化建议:\n")
  cat("• 对于大量化合物，设置 delay = 1-2 秒\n")
  cat("• 避免重复查询已有数据的化合物\n")
  cat("• 网络不稳定时可以增加重试次数\n\n")
}

# 运行示例
example_usage()
explain_workflow()
show_notes()

cat("现在您可以使用增强后的函数进行化合物信息查询了！\n")
cat("如果 PubChem 查询失败，系统会自动尝试 ChemSpider 数据库。\n")
