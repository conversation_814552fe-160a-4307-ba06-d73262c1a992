# 调试查询功能
library(httr)
library(jsonlite)
library(openxlsx)

# 加载主函数
source("pubchem.R")

# 测试单个查询函数
test_single_query <- function() {
  cat("=== 测试单个查询函数 ===\n\n")
  
  # 测试 PubChem 查询
  cat("1. 测试 PubChem 查询 'Caffeine':\n")
  result1 <- get_compound_info_from_pubchem("Caffeine", delay = 1)
  print(result1)
  cat("\n")
  
  # 测试 PubChem InChIKey 查询
  cat("2. 测试 PubChem InChIKey 查询:\n")
  result2 <- get_compound_info_by_inchikey("RYYVLZVUVIJVGH-UHFFFAOYSA-N", delay = 1)
  print(result2)
  cat("\n")
  
  # 测试 ChEBI 查询
  cat("3. 测试 ChEBI 查询 'Caffeine':\n")
  result3 <- get_compound_info_from_chebi("Caffeine", delay = 1)
  print(result3)
  cat("\n")
}

# 创建最小测试数据
create_minimal_test <- function() {
  test_data <- data.frame(
    NAME = c("Caffeine", "Aspirin", "Water"),
    InChIKey = c("", "", ""),
    SMILES = c(NA, NA, NA),
    MolecularFormula = c(NA, NA, NA),
    MolecularWeight = c(NA, NA, NA),
    stringsAsFactors = FALSE
  )
  
  write.xlsx(test_data, "minimal_test.xlsx")
  cat("创建了最小测试文件: minimal_test.xlsx\n")
  return("minimal_test.xlsx")
}

# 测试完整流程
test_full_process <- function() {
  cat("=== 测试完整查询流程 ===\n\n")
  
  # 创建测试文件
  test_file <- create_minimal_test()
  
  # 运行查询
  tryCatch({
    cat("开始查询...\n")
    Add_pubchem_info_pro(
      file_path = test_file,
      output_path = "debug_results.xlsx",
      delay = 1,
      force_query_all = TRUE,
      fields_to_update = c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")
    )
    
    # 读取并显示结果
    if (file.exists("debug_results.xlsx")) {
      results <- read.xlsx("debug_results.xlsx")
      cat("\n=== 查询结果 ===\n")
      print(results)
    }
    
  }, error = function(e) {
    cat("错误:", e$message, "\n")
    cat("错误详情:\n")
    print(e)
  })
}

# 检查网络连接
test_network <- function() {
  cat("=== 测试网络连接 ===\n\n")
  
  # 测试 PubChem 连接
  cat("1. 测试 PubChem 连接:\n")
  pubchem_url <- "https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/water/property/MolecularFormula/JSON"
  response1 <- try(GET(pubchem_url, timeout(10)), silent = TRUE)
  if (inherits(response1, "try-error")) {
    cat("❌ PubChem 连接失败\n")
  } else {
    cat("✅ PubChem 连接成功, 状态码:", status_code(response1), "\n")
  }
  
  # 测试 ChEBI 连接
  cat("2. 测试 ChEBI 连接:\n")
  chebi_url <- "https://www.ebi.ac.uk/webservices/chebi/2.0/test/getLiteEntity?search=water&searchCategory=ALL&maximumResults=1"
  response2 <- try(GET(chebi_url, timeout(10)), silent = TRUE)
  if (inherits(response2, "try-error")) {
    cat("❌ ChEBI 连接失败\n")
  } else {
    cat("✅ ChEBI 连接成功, 状态码:", status_code(response2), "\n")
  }
  
  cat("\n")
}

# 运行所有测试
cat("开始调试查询功能...\n\n")

# 1. 检查网络连接
test_network()

# 2. 测试单个查询函数
test_single_query()

# 3. 测试完整流程
test_full_process()

cat("调试完成！\n")
