# ============================================================================
# 文件夹合并工具函数库
# 功能：将多个子文件夹中的文件合并到一个目标文件夹
# 支持串行和并行处理模式
# ============================================================================

#' 检查并安装必要的包
#' @description 自动检查并安装运行所需的R包
check_and_install_packages <- function() {
  required_packages <- c("parallel", "foreach", "doParallel")
  missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]

  if (length(missing_packages) > 0) {
    cat("正在安装缺失的包:", paste(missing_packages, collapse = ", "), "\n")
    install.packages(missing_packages)
  }

  # 加载包
  suppressMessages({
    library(parallel)
    library(foreach)
    library(doParallel)
  })

  cat("所有必要的包已加载完成\n")
}

#' 生成唯一文件名
#' @param filename 原始文件名
#' @param output_path 输出路径
#' @return 唯一的文件路径
generate_unique_filename <- function(filename, output_path) {
  dest_file <- file.path(output_path, filename)

  if (!file.exists(dest_file)) {
    return(dest_file)
  }

  # 如果文件名冲突，添加序号
  counter <- 1
  repeat {
    name_parts <- tools::file_path_sans_ext(filename)
    extension <- tools::file_ext(filename)

    if (extension == "") {
      new_name <- paste0(name_parts, "_", counter)
    } else {
      new_name <- paste0(name_parts, "_", counter, ".", extension)
    }

    dest_file <- file.path(output_path, new_name)

    if (!file.exists(dest_file)) {
      break
    }

    counter <- counter + 1
  }

  return(dest_file)
}

#' 处理单个文件的复制
#' @param file 源文件路径
#' @param output_path 目标文件夹路径
#' @return 包含处理结果的列表
process_single_file <- function(file, output_path) {
  tryCatch({
    filename <- basename(file)
    dest_file <- generate_unique_filename(filename, output_path)

    # 复制文件
    success <- file.copy(file, dest_file, overwrite = FALSE)

    if (success) {
      return(list(
        success = TRUE,
        source = file,
        dest = dest_file,
        size = file.info(file)$size
      ))
    } else {
      return(list(
        success = FALSE,
        source = file,
        dest = dest_file,
        error = "文件复制失败"
      ))
    }
  }, error = function(e) {
    return(list(
      success = FALSE,
      source = file,
      dest = "",
      error = paste("错误:", e$message)
    ))
  })
}

# 并行合并文件夹函数
merge_folders_parallel <- function(input_path, output_path, num_cores = NULL) {
  # 检查输入路径是否存在
  if (!dir.exists(input_path)) {
    stop("输入路径不存在: ", input_path)
  }

  # 创建输出文件夹
  if (!dir.exists(output_path)) {
    dir.create(output_path, recursive = TRUE)
  }

  # 设置核心数
  if (is.null(num_cores)) {
    num_cores <- max(1, detectCores() - 1)  # 保留一个核心给系统
  }

  cat("使用", num_cores, "个CPU核心进行并行处理\n")

  # 获取所有子文件夹
  subfolders <- list.dirs(input_path, recursive = TRUE, full.names = TRUE)
  subfolders <- subfolders[subfolders != input_path]  # 排除根目录

  # 收集所有文件
  all_files <- c()
  for (folder in subfolders) {
    files <- list.files(folder, full.names = TRUE, recursive = FALSE)
    files <- files[!dir.exists(files)]  # 只要文件，不要文件夹
    all_files <- c(all_files, files)
  }

  if (length(all_files) == 0) {
    cat("没有找到任何文件\n")
    return()
  }

  cat("找到", length(all_files), "个文件需要处理\n")

  # 设置并行后端
  cl <- makeCluster(num_cores)
  registerDoParallel(cl)

  # 导出必要的函数和变量到工作节点
  clusterExport(cl, c("process_file", "output_path"))

  start_time <- Sys.time()

  # 并行处理文件
  results <- foreach(file = all_files, .combine = rbind, .packages = c("tools")) %dopar% {
    result <- process_file(file, output_path)
    data.frame(
      success = result$success,
      source = result$source,
      dest = result$dest,
      error = ifelse(result$success, "", result$error %||% ""),
      stringsAsFactors = FALSE
    )
  }

  # 停止并行后端
  stopCluster(cl)

  end_time <- Sys.time()
  processing_time <- end_time - start_time

  # 统计结果
  successful_files <- sum(results$success)
  failed_files <- sum(!results$success)

  cat("\n=== 处理完成 ===\n")
  cat("总文件数:", nrow(results), "\n")
  cat("成功复制:", successful_files, "\n")
  cat("复制失败:", failed_files, "\n")
  cat("处理时间:", round(processing_time, 2), attr(processing_time, "units"), "\n")

  # 显示失败的文件
  if (failed_files > 0) {
    cat("\n失败的文件:\n")
    failed_results <- results[!results$success, ]
    for (i in 1:nrow(failed_results)) {
      cat("  ", failed_results$source[i], "->", failed_results$dest[i], "(", failed_results$error[i], ")\n")
    }
  }

  return(results)
}

# 串行版本（保留原始功能）
merge_folders <- function(input_path, output_path) {
  # 检查输入路径是否存在
  if (!dir.exists(input_path)) {
    stop("输入路径不存在: ", input_path)
  }

  # 创建输出文件夹
  if (!dir.exists(output_path)) {
    dir.create(output_path, recursive = TRUE)
  }

  # 获取所有子文件夹
  subfolders <- list.dirs(input_path, recursive = TRUE, full.names = TRUE)
  subfolders <- subfolders[subfolders != input_path]  # 排除根目录

  start_time <- Sys.time()

  # 遍历所有子文件夹
  for (folder in subfolders) {
    # 获取文件夹中的所有文件
    files <- list.files(folder, full.names = TRUE, recursive = FALSE)
    files <- files[!dir.exists(files)]  # 只要文件，不要文件夹

    # 复制文件到输出文件夹
    for (file in files) {
      filename <- basename(file)
      dest_file <- file.path(output_path, filename)

      # 如果文件名冲突，添加序号
      counter <- 1
      while (file.exists(dest_file)) {
        name_parts <- tools::file_path_sans_ext(filename)
        extension <- tools::file_ext(filename)
        new_name <- paste0(name_parts, "_", counter, ".", extension)
        dest_file <- file.path(output_path, new_name)
        counter <- counter + 1
      }

      file.copy(file, dest_file)
      cat("复制:", file, "->", dest_file, "\n")
    }
  }

  end_time <- Sys.time()
  processing_time <- end_time - start_time

  cat("合并完成！总共处理了", length(list.files(output_path)), "个文件\n")
  cat("处理时间:", round(processing_time, 2), attr(processing_time, "units"), "\n")
}

# 使用示例
input_folder <- "输入文件夹路径"    # 修改为你的输入路径
output_folder <- "输出文件夹路径"  # 修改为你的输出路径

# 方法1: 使用并行处理（推荐，速度更快）
# 自动检测CPU核心数
results <- merge_folders_parallel(input_folder, output_folder)

# 或者手动指定核心数
# results <- merge_folders_parallel(input_folder, output_folder, num_cores = 4)

# 方法2: 使用串行处理（原始版本）
# merge_folders(input_folder, output_folder)

# 安装必要的包（如果还没有安装）
# install.packages(c("parallel", "foreach", "doParallel"))