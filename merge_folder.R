# 合并文件夹脚本
merge_folders <- function(input_path, output_path) {
  # 检查输入路径是否存在
  if (!dir.exists(input_path)) {
    stop("输入路径不存在: ", input_path)
  }

  # 创建输出文件夹
  if (!dir.exists(output_path)) {
    dir.create(output_path, recursive = TRUE)
  }

  # 获取所有子文件夹
  subfolders <- list.dirs(input_path, recursive = TRUE, full.names = TRUE)
  subfolders <- subfolders[subfolders != input_path]  # 排除根目录

  # 遍历所有子文件夹
  for (folder in subfolders) {
    # 获取文件夹中的所有文件
    files <- list.files(folder, full.names = TRUE, recursive = FALSE)
    files <- files[!dir.exists(files)]  # 只要文件，不要文件夹

    # 复制文件到输出文件夹
    for (file in files) {
      filename <- basename(file)
      dest_file <- file.path(output_path, filename)

      # 如果文件名冲突，添加序号
      counter <- 1
      while (file.exists(dest_file)) {
        name_parts <- tools::file_path_sans_ext(filename)
        extension <- tools::file_ext(filename)
        new_name <- paste0(name_parts, "_", counter, ".", extension)
        dest_file <- file.path(output_path, new_name)
        counter <- counter + 1
      }

      file.copy(file, dest_file)
      cat("复制:", file, "->", dest_file, "\n")
    }
  }

  cat("合并完成！总共处理了", length(list.files(output_path)), "个文件\n")
}

# 使用示例
input_folder <- "输入文件夹路径"    # 修改为你的输入路径
output_folder <- "输出文件夹路径"  # 修改为你的输出路径

merge_folders(input_folder, output_folder)