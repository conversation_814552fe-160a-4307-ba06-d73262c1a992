# 修复版本的化合物查询函数
# 专注于解决查询失败的问题

Add_pubchem_info_pro <- function(file_path, output_path = NULL,
                             delay = 1,  # 增加默认延迟
                             force_query_all = FALSE,
                             fields_to_update = c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")) {

  library(httr)
  library(jsonlite)
  library(readxl)
  library(openxlsx)
  library(stringr)

  allowed_fields <- c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")
  fields_to_update <- intersect(fields_to_update, allowed_fields)
  if (length(fields_to_update) == 0) stop("❌ 请至少选择一个有效字段进行查询")

  # 辅助函数
  `%||%` <- function(a, b) if (!is.null(a)) a else b

  get_valid_smiles <- function(props) {
    if (is.list(props) || is.data.frame(props)) {
      smiles <- props$SMILES
      if (!is.null(smiles) && nzchar(smiles)) return(as.character(smiles))

      iso <- props$IsomericSMILES
      if (!is.null(iso) && nzchar(iso)) return(as.character(iso))

      cano <- props$CanonicalSMILES
      if (!is.null(cano) && nzchar(cano)) return(as.character(cano))

      conn <- props$ConnectivitySMILES
      if (!is.null(conn) && nzchar(conn)) return(as.character(conn))
    }
    return(NA)
  }

  # 改进的 PubChem 查询函数
  get_compound_info_from_pubchem <- function(compound_name, max_retries = 5, delay = 1) {
    if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    name_encoded <- URLencode(compound_name, reserved = TRUE)
    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/", name_encoded,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    cat(sprintf("🔍 查询 PubChem: %s\n", compound_name))
    cat(sprintf("📡 URL: %s\n", url))

    for (attempt in 1:max_retries) {
      cat(sprintf("   尝试 %d/%d...\n", attempt, max_retries))
      
      response <- try({
        GET(url, 
            timeout(60),  # 增加超时时间
            add_headers(
              "User-Agent" = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            ))
      }, silent = TRUE)
      
      if (inherits(response, "try-error")) {
        cat(sprintf("   ❌ 网络错误: %s\n", as.character(response)))
        Sys.sleep(delay * attempt)
        next
      }

      cat(sprintf("   📊 HTTP状态码: %d\n", status_code(response)))

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          cat("   ❌ JSON解析错误\n")
          Sys.sleep(delay)
          next
        }

        if (is.null(content_json$PropertyTable) || is.null(content_json$PropertyTable$Properties)) {
          cat("   ❌ 响应中没有找到属性数据\n")
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) {
          cat("   ❌ 属性表为空\n")
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        }
        
        props <- as.list(props_df[1, ])
        
        result <- list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA
        )
        
        cat("   ✅ PubChem 查询成功\n")
        return(result)
        
      } else if (status_code(response) == 404) {
        cat("   ❌ 化合物未找到 (404)\n")
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
      } else {
        cat(sprintf("   ❌ HTTP错误 %d\n", status_code(response)))
        if (status_code(response) == 429) {
          cat("   ⏳ 请求过于频繁，延长等待时间\n")
          Sys.sleep(delay * attempt * 2)
        } else {
          Sys.sleep(delay * attempt)
        }
      }
    }

    cat("   ❌ 所有重试都失败了\n")
    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  # 通过 InChIKey 查询
  get_compound_info_by_inchikey <- function(inchikey, max_retries = 5, delay = 1) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/inchikey/", inchikey,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    cat(sprintf("🔍 通过 InChIKey 查询: %s\n", inchikey))

    for (attempt in 1:max_retries) {
      response <- try({
        GET(url, 
            timeout(60),
            add_headers(
              "User-Agent" = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            ))
      }, silent = TRUE)
      
      if (inherits(response, "try-error")) {
        cat(sprintf("   ❌ 网络错误 (尝试 %d/%d)\n", attempt, max_retries))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          Sys.sleep(delay)
          next
        }

        if (is.null(content_json$PropertyTable) || is.null(content_json$PropertyTable$Properties)) {
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        props <- as.list(props_df[1, ])

        result <- list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA
        )
        
        cat("   ✅ InChIKey 查询成功\n")
        return(result)
        
      } else if (status_code(response) == 404) {
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
      } else {
        cat(sprintf("   ❌ HTTP错误 %d (尝试 %d/%d)\n", status_code(response), attempt, max_retries))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  # 简化的备用查询 - 使用 NCI/CADD 数据库
  get_compound_info_from_nci <- function(compound_name, max_retries = 3, delay = 1) {
    if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    # 尝试使用 NCI/CADD Chemical Identifier Resolver
    name_encoded <- URLencode(compound_name, reserved = TRUE)
    
    # 获取 SMILES
    smiles_url <- paste0("https://cactus.nci.nih.gov/chemical/structure/", name_encoded, "/smiles")
    # 获取分子式
    formula_url <- paste0("https://cactus.nci.nih.gov/chemical/structure/", name_encoded, "/formula")
    # 获取分子量
    mw_url <- paste0("https://cactus.nci.nih.gov/chemical/structure/", name_encoded, "/mw")
    
    cat(sprintf("🔍 尝试 NCI/CADD 数据库: %s\n", compound_name))
    
    result <- list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA)
    
    # 查询 SMILES
    tryCatch({
      response <- GET(smiles_url, timeout(30))
      if (status_code(response) == 200) {
        smiles <- trimws(rawToChar(response$content))
        if (nchar(smiles) > 0 && !grepl("Error", smiles, ignore.case = TRUE)) {
          result$SMILES <- smiles
        }
      }
    }, error = function(e) {})
    
    # 查询分子式
    tryCatch({
      response <- GET(formula_url, timeout(30))
      if (status_code(response) == 200) {
        formula <- trimws(rawToChar(response$content))
        if (nchar(formula) > 0 && !grepl("Error", formula, ignore.case = TRUE)) {
          result$MolecularFormula <- formula
        }
      }
    }, error = function(e) {})
    
    # 查询分子量
    tryCatch({
      response <- GET(mw_url, timeout(30))
      if (status_code(response) == 200) {
        mw <- trimws(rawToChar(response$content))
        if (nchar(mw) > 0 && !grepl("Error", mw, ignore.case = TRUE)) {
          result$MolecularWeight <- as.numeric(mw)
        }
      }
    }, error = function(e) {})
    
    if (!is.na(result$SMILES) || !is.na(result$MolecularFormula)) {
      cat("   ✅ NCI/CADD 查询成功\n")
    }
    
    return(result)
  }

  # 主查询逻辑
  add_compound_info_to_df <- function(df, compound_col, delay = 1) {
    for (field in allowed_fields) {
      if (!(field %in% colnames(df))) df[[field]] <- NA
    }

    if (force_query_all) {
      need_query <- seq_len(nrow(df))
    } else {
      need_query <- which(
        is.na(df$InChIKey) | df$InChIKey == "" |
        (("SMILES" %in% fields_to_update) & (is.na(df$SMILES) | df$SMILES == ""))
      )
    }

    if (length(need_query) == 0) {
      cat("✅ 所有数据已完整，无需查询\n")
      return(df)
    }

    cat(sprintf("📋 需要查询 %d 个化合物\n", length(need_query)))

    for (i in need_query) {
      compound_name <- df[[compound_col]][i]
      cat(sprintf("\n🔍 查询 (%d/%d): %s\n", which(need_query == i), length(need_query), compound_name))

      # 首先尝试 PubChem 通过名称查询
      result <- get_compound_info_from_pubchem(compound_name, delay = delay)

      # 如果 NAME 查询失败，并且存在 InChIKey，尝试用 InChIKey 查 PubChem
      if (all(is.na(result[fields_to_update]) | result[fields_to_update] == "") &&
          !is.na(df$InChIKey[i]) && df$InChIKey[i] != "") {
        cat("🔁 PubChem 名称查询失败，尝试使用 InChIKey...\n")
        result <- get_compound_info_by_inchikey(df$InChIKey[i], delay = delay)
      }

      # 如果 PubChem 都查询失败，尝试 NCI/CADD
      if (all(is.na(result[fields_to_update]) | result[fields_to_update] == "")) {
        cat("🌐 PubChem 查询失败，尝试 NCI/CADD 数据库...\n")
        result <- get_compound_info_from_nci(compound_name, delay = delay)
      }

      # 更新数据
      for (field in fields_to_update) {
        if (!is.na(result[[field]])) {
          df[[field]][i] <- result[[field]]
        }
      }

      found <- fields_to_update[!is.na(result[fields_to_update])]
      if (length(found) > 0) {
        cat("✅ 查询成功 - 获得:", paste(found, collapse = ", "), "\n")
      } else {
        cat("❌ 所有数据库查询失败\n")
      }

      cat(sprintf("⏳ 等待 %.1f 秒...\n", delay))
      Sys.sleep(delay)
    }

    return(df)
  }

  # --- 主逻辑 ---
  cat("📖 读取文件:", file_path, "\n")
  df <- read_excel(file_path)
  if (!"NAME" %in% colnames(df)) stop("❌ 文件中未找到 'NAME' 列")
  cat(sprintf("📊 文件包含 %d 行数据\n", nrow(df)))

  cat("🧹 清洗化合物名称...\n")
  df$NAME <- str_replace(df$NAME, "^Massbank:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^ReSpect:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCGC\\d+-\\d+!", "")
  df$NAME <- str_replace(df$NAME, "^MoNA:\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "MassbankEU:SM\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCG\\d+!", "")
  df$NAME <- str_extract(df$NAME, "[^|]+")
  df$NAME <- str_extract(df$NAME, "^[^;]+")
  df$NAME <- trimws(df$NAME)

  sample_names <- head(df$NAME[!is.na(df$NAME) & df$NAME != ""], 3)
  if (length(sample_names) > 0) cat("📝 样本化合物名称:", paste(sample_names, collapse = ", "), "...\n")

  cat("🔍 开始查询化合物数据库 (PubChem + NCI/CADD)...\n")
  df_result <- add_compound_info_to_df(df, compound_col = "NAME", delay = delay)

  if (is.null(output_path)) {
    date_str <- format(Sys.Date(), "%Y%m%d")
    base_name <- tools::file_path_sans_ext(basename(file_path))
    output_path <- paste0(base_name, "_updated_", date_str, ".xlsx")
  }

  cat("💾 保存结果到:", output_path, "\n")
  write.xlsx(df_result, output_path)

  smiles_count <- sum(!is.na(df_result$SMILES) & df_result$SMILES != "", na.rm = TRUE)
  inchikey_count <- sum(!is.na(df_result$InChIKey) & df_result$InChIKey != "", na.rm = TRUE)

  cat("📦 查询完成！结果已保存到:", output_path, "\n")
  cat(sprintf("📈 统计结果: SMILES: %d/%d, InChIKey: %d/%d\n",
              smiles_count, nrow(df_result), inchikey_count, nrow(df_result)))
}
