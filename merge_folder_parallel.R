# 并行文件夹合并工具
# 功能：将多个子文件夹中的文件并行合并到一个目标文件夹

library(parallel)
library(foreach)
library(doParallel)

#' 处理单个文件的复制
#' @param file 源文件路径
#' @param output_path 目标文件夹路径
#' @return 包含处理结果的列表
process_file_1 <- function(file, output_path) {
  tryCatch({
    filename <- basename(file)
    dest_file <- file.path(output_path, filename)
    
    # 如果文件名冲突，添加序号
    counter <- 1
    while (file.exists(dest_file)) {
      name_parts <- tools::file_path_sans_ext(filename)
      extension <- tools::file_ext(filename)
      
      if (extension == "") {
        new_name <- paste0(name_parts, "_", counter)
      } else {
        new_name <- paste0(name_parts, "_", counter, ".", extension)
      }
      
      dest_file <- file.path(output_path, new_name)
      counter <- counter + 1
    }
    
    # 复制文件
    success <- file.copy(file, dest_file, overwrite = FALSE)
    
    if (success) {
      return(list(
        success = TRUE,
        source = file,
        dest = dest_file,
        size = file.info(file)$size
      ))
    } else {
      return(list(
        success = FALSE,
        source = file,
        dest = dest_file,
        error = "文件复制失败"
      ))
    }
  }, error = function(e) {
    return(list(
      success = FALSE,
      source = file,
      dest = "",
      error = paste("错误:", e$message)
    ))
  })
}

#' 并行合并文件夹函数
#' @param input_path 输入文件夹路径
#' @param output_path 输出文件夹路径
#' @param num_cores 使用的CPU核心数，默认为NULL（自动检测）
#' @param verbose 是否显示详细信息，默认为TRUE
#' @return 包含处理结果的数据框
merge_folders <- function(input_path, output_path, num_cores = NULL, verbose = TRUE) {
  # 检查输入路径是否存在
  if (!dir.exists(input_path)) {
    stop("输入路径不存在: ", input_path)
  }
  
  # 创建输出文件夹
  if (!dir.exists(output_path)) {
    dir.create(output_path, recursive = TRUE)
  }
  
  # 设置核心数
  if (is.null(num_cores)) {
    num_cores <- max(1, detectCores() - 1)  # 保留一个核心给系统
  }
  
  if (verbose) {
    cat("使用", num_cores, "个CPU核心进行并行处理\n")
  }
  
  # 获取所有子文件夹
  subfolders <- list.dirs(input_path, recursive = TRUE, full.names = TRUE)
  subfolders <- subfolders[subfolders != input_path]  # 排除根目录
  
  # 收集所有文件
  all_files <- c()
  for (folder in subfolders) {
    files <- list.files(folder, full.names = TRUE, recursive = FALSE)
    files <- files[!dir.exists(files)]  # 只要文件，不要文件夹
    all_files <- c(all_files, files)
  }
  
  if (length(all_files) == 0) {
    if (verbose) cat("没有找到任何文件\n")
    return(data.frame())
  }
  
  if (verbose) {
    cat("找到", length(all_files), "个文件需要处理\n")
  }
  
  # 设置并行后端
  cl <- makeCluster(num_cores)
  registerDoParallel(cl)
  
  # 导出必要的函数和变量到工作节点
  clusterExport(cl, c("process_file_1"), envir = environment())
  clusterEvalQ(cl, library(tools))

  start_time <- Sys.time()

  # 并行处理文件
  results <- foreach(file = all_files, output_path = output_path, .combine = rbind, .packages = c("tools")) %dopar% {
    result <- process_file_1(file, output_path)
    data.frame(
      success = result$success,
      source = result$source,
      dest = result$dest,
      error = ifelse(result$success, "", result$error %||% ""),
      size = ifelse(result$success, result$size %||% 0, 0),
      stringsAsFactors = FALSE
    )
  }
  
  # 停止并行后端
  stopCluster(cl)
  
  end_time <- Sys.time()
  processing_time <- end_time - start_time
  
  # 统计结果
  successful_files <- sum(results$success)
  failed_files <- sum(!results$success)
  total_size <- sum(results$size[results$success], na.rm = TRUE)
  
  if (verbose) {
    cat("\n=== 处理完成 ===\n")
    cat("总文件数:", nrow(results), "\n")
    cat("成功复制:", successful_files, "\n")
    cat("复制失败:", failed_files, "\n")
    cat("总大小:", round(total_size / 1024 / 1024, 2), "MB\n")
    cat("处理时间:", round(processing_time, 2), attr(processing_time, "units"), "\n")
    
    # 显示失败的文件
    if (failed_files > 0) {
      cat("\n失败的文件:\n")
      failed_results <- results[!results$success, ]
      for (i in 1:nrow(failed_results)) {
        cat("  ", failed_results$source[i], "->", failed_results$dest[i], "(", failed_results$error[i], ")\n")
      }
    }
  }
  
  return(results)
}

