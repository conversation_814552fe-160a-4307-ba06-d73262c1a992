Add_pubchem_info_pro <- function(file_path, output_path = NULL,
                             delay = 0.5,
                             force_query_all = FALSE,
                             fields_to_update = c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")) {

  library(httr)
  library(jsonlite)
  library(readxl)
  library(openxlsx)
  library(stringr)

  allowed_fields <- c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")
  fields_to_update <- intersect(fields_to_update, allowed_fields)
  if (length(fields_to_update) == 0) stop("❌ 请至少选择一个有效字段进行查询")

  get_valid_smiles <- function(props) {
    if (is.list(props) || is.data.frame(props)) {
      smiles <- props$SMILES
      if (!is.null(smiles) && nzchar(smiles)) return(as.character(smiles))

      iso <- props$IsomericSMILES
      if (!is.null(iso) && nzchar(iso)) return(as.character(iso))

      cano <- props$CanonicalSMILES
      if (!is.null(cano) && nzchar(cano)) return(as.character(cano))

      conn <- props$ConnectivitySMILES
      if (!is.null(conn) && nzchar(conn)) return(as.character(conn))
    }
    return(NA)
  }

  get_compound_info_from_pubchem <- function(compound_name, max_retries = 3, delay = 1) {
    if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    name_encoded <- URLencode(compound_name, reserved = TRUE)
    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/", name_encoded,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        cat(sprintf("❌ 网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          cat(sprintf("❌ JSON解析错误: %s\n", compound_name))
          Sys.sleep(delay * attempt)
          next
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        props <- as.list(props_df[1, ])

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA
        ))
      } else if (status_code(response) == 404) {
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
      } else {
        cat(sprintf("❌ HTTP错误 %d (尝试 %d/%d): %s\n", status_code(response), attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  get_compound_info_by_inchikey <- function(inchikey, max_retries = 3, delay = 1) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/inchikey/", inchikey,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        cat(sprintf("❌ 网络错误 (InChIKey 尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          Sys.sleep(delay * attempt)
          next
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        props <- as.list(props_df[1, ])

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA
        ))
      } else if (status_code(response) == 404) {
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
      } else {
        cat(sprintf("❌ HTTP错误 %d (InChIKey 尝试 %d/%d): %s\n", status_code(response), attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  # ChemSpider 查询函数
  get_compound_info_from_chemspider <- function(compound_name, max_retries = 3, delay = 1) {
    if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    # ChemSpider 搜索 API (免费版本，无需API key)
    name_encoded <- URLencode(compound_name, reserved = TRUE)
    search_url <- paste0("http://www.chemspider.com/Search.asmx/SimpleSearch?query=", name_encoded, "&token=")

    for (attempt in 1:max_retries) {
      response <- try(GET(search_url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        cat(sprintf("❌ ChemSpider 网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        # 解析XML响应
        content_text <- rawToChar(response$content)

        # 提取CSID (ChemSpider ID)
        csid_match <- regexpr('<int[^>]*>([0-9]+)</int>', content_text)
        if (csid_match[1] > 0) {
          csid <- regmatches(content_text, csid_match)
          csid <- gsub('<[^>]*>', '', csid)

          # 使用CSID获取化合物详细信息
          detail_result <- get_chemspider_details_by_csid(csid, max_retries, delay)
          if (!all(is.na(detail_result[fields_to_update]))) {
            return(detail_result)
          }
        }
      }

      Sys.sleep(delay * attempt)
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  get_chemspider_info_by_inchikey <- function(inchikey, max_retries = 3, delay = 1) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    # 使用 InChIKey 搜索
    search_url <- paste0("http://www.chemspider.com/Search.asmx/SimpleSearch?query=", inchikey, "&token=")

    for (attempt in 1:max_retries) {
      response <- try(GET(search_url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        cat(sprintf("❌ ChemSpider InChIKey 网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_text <- rawToChar(response$content)

        # 提取CSID
        csid_match <- regexpr('<int[^>]*>([0-9]+)</int>', content_text)
        if (csid_match[1] > 0) {
          csid <- regmatches(content_text, csid_match)
          csid <- gsub('<[^>]*>', '', csid)

          # 使用CSID获取详细信息
          detail_result <- get_chemspider_details_by_csid(csid, max_retries, delay)
          if (!all(is.na(detail_result[fields_to_update]))) {
            return(detail_result)
          }
        }
      }

      Sys.sleep(delay * attempt)
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  get_chemspider_details_by_csid <- function(csid, max_retries = 3, delay = 1) {
    # 获取化合物详细信息的URL
    detail_url <- paste0("http://www.chemspider.com/Chemical-Structure.", csid, ".html")

    for (attempt in 1:max_retries) {
      response <- try(GET(detail_url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_text <- rawToChar(response$content)

        # 尝试从页面内容中提取信息
        result <- list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA)

        # 提取SMILES (寻找页面中的SMILES信息)
        smiles_pattern <- 'SMILES[^>]*>([^<]+)<'
        smiles_match <- regexpr(smiles_pattern, content_text, ignore.case = TRUE)
        if (smiles_match[1] > 0) {
          smiles_text <- regmatches(content_text, smiles_match)
          smiles <- gsub('.*>([^<]+)<.*', '\\1', smiles_text)
          if (nchar(trimws(smiles)) > 0) result$SMILES <- trimws(smiles)
        }

        # 提取分子式
        formula_pattern <- 'Molecular Formula[^>]*>([^<]+)<'
        formula_match <- regexpr(formula_pattern, content_text, ignore.case = TRUE)
        if (formula_match[1] > 0) {
          formula_text <- regmatches(content_text, formula_match)
          formula <- gsub('.*>([^<]+)<.*', '\\1', formula_text)
          if (nchar(trimws(formula)) > 0) result$MolecularFormula <- trimws(formula)
        }

        # 提取分子量
        weight_pattern <- 'Molecular Weight[^>]*>([0-9.]+)'
        weight_match <- regexpr(weight_pattern, content_text, ignore.case = TRUE)
        if (weight_match[1] > 0) {
          weight_text <- regmatches(content_text, weight_match)
          weight <- gsub('.*>([0-9.]+).*', '\\1', weight_text)
          if (nchar(trimws(weight)) > 0) result$MolecularWeight <- as.numeric(trimws(weight))
        }

        # 提取InChIKey
        inchikey_pattern <- 'InChIKey[^>]*>([A-Z-]+)'
        inchikey_match <- regexpr(inchikey_pattern, content_text, ignore.case = TRUE)
        if (inchikey_match[1] > 0) {
          inchikey_text <- regmatches(content_text, inchikey_match)
          inchikey <- gsub('.*>([A-Z-]+).*', '\\1', inchikey_text)
          if (nchar(trimws(inchikey)) > 0) result$InChIKey <- trimws(inchikey)
        }

        return(result)
      }

      Sys.sleep(delay * attempt)
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  add_compound_info_to_df <- function(df, compound_col, delay = 1) {
    for (field in allowed_fields) {
      if (!(field %in% colnames(df))) df[[field]] <- NA
    }

    if (force_query_all) {
      need_query <- seq_len(nrow(df))
    } else {
      need_query <- which(
        is.na(df$InChIKey) | df$InChIKey == "" |
        (("SMILES" %in% fields_to_update) & (is.na(df$SMILES) | df$SMILES == ""))
      )
    }

    if (length(need_query) == 0) {
      cat("✅ 所有数据已完整，无需查询\n")
      return(df)
    }

    for (i in need_query) {
      compound_name <- df[[compound_col]][i]
      cat(sprintf("🔍 查询 (%d/%d): %s\n", which(need_query == i), length(need_query), compound_name))

      # 首先尝试 PubChem 通过名称查询
      result <- get_compound_info_from_pubchem(compound_name, delay = delay)

      # 如果 NAME 查询失败，并且存在 InChIKey，尝试用 InChIKey 查 PubChem
      if (all(is.na(result[fields_to_update]) | result[fields_to_update] == "") &&
          !is.na(df$InChIKey[i]) && df$InChIKey[i] != "") {
        cat("🔁 PubChem 名称查询失败，尝试使用 InChIKey 查询 PubChem...\n")
        result <- get_compound_info_by_inchikey(df$InChIKey[i], delay = delay)
      }

      # 如果 PubChem 都查询失败，尝试 ChemSpider
      if (all(is.na(result[fields_to_update]) | result[fields_to_update] == "")) {
        cat("🌐 PubChem 查询失败，尝试 ChemSpider 数据库...\n")

        # 首先尝试通过名称查询 ChemSpider
        result <- get_compound_info_from_chemspider(compound_name, delay = delay)

        # 如果名称查询失败，并且存在 InChIKey，尝试用 InChIKey 查 ChemSpider
        if (all(is.na(result[fields_to_update]) | result[fields_to_update] == "") &&
            !is.na(df$InChIKey[i]) && df$InChIKey[i] != "") {
          cat("🔁 ChemSpider 名称查询失败，尝试使用 InChIKey 查询 ChemSpider...\n")
          result <- get_chemspider_info_by_inchikey(df$InChIKey[i], delay = delay)
        }
      }

      for (field in fields_to_update) {
        if (!is.na(result[[field]])) {
          df[[field]][i] <- result[[field]]
        }
      }

      found <- fields_to_update[!is.na(result[fields_to_update])]
      if (length(found) > 0) {
        cat("✅ 查询成功 - 获得:", paste(found, collapse = ", "), "\n")
      } else {
        cat("❌ 所有数据库查询失败或结果为空\n")
      }

      Sys.sleep(delay)
    }

    return(df)
  }

  `%||%` <- function(a, b) if (!is.null(a)) a else b

  # --- 主逻辑 ---
  cat("📖 读取文件:", file_path, "\n")
  df <- read_excel(file_path)
  if (!"NAME" %in% colnames(df)) stop("❌ 文件中未找到 'NAME' 列")
  cat(sprintf("📊 文件包含 %d 行数据\n", nrow(df)))

  cat("🧹 清洗化合物名称...\n")
  df$NAME <- str_replace(df$NAME, "^Massbank:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^ReSpect:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCGC\\d+-\\d+!", "")
  df$NAME <- str_replace(df$NAME, "^MoNA:\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "MassbankEU:SM\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCG\\d+!", "")
  df$NAME <- str_extract(df$NAME, "[^|]+")
  df$NAME <- str_extract(df$NAME, "^[^;]+")
  df$NAME <- trimws(df$NAME)

  sample_names <- head(df$NAME[!is.na(df$NAME) & df$NAME != ""], 3)
  if (length(sample_names) > 0) cat("📝 样本化合物名称:", paste(sample_names, collapse = ", "), "...\n")

  cat("🔍 开始查询化合物数据库 (PubChem + ChemSpider)...\n")
  df_result <- add_compound_info_to_df(df, compound_col = "NAME", delay = delay)

  if (is.null(output_path)) {
    date_str <- format(Sys.Date(), "%Y%m%d")
    base_name <- tools::file_path_sans_ext(basename(file_path))
    output_path <- paste0(base_name, "_updated_", date_str, ".xlsx")
  }

  cat("💾 保存结果到:", output_path, "\n")
  write.xlsx(df_result, output_path)

  smiles_count <- sum(!is.na(df_result$SMILES) & df_result$SMILES != "", na.rm = TRUE)
  inchikey_count <- sum(!is.na(df_result$InChIKey) & df_result$InChIKey != "", na.rm = TRUE)

  cat("📦 查询完成！结果已保存到:", output_path, "\n")
  cat(sprintf("📈 统计结果: SMILES: %d/%d, InChIKey: %d/%d\n",
              smiles_count, nrow(df_result), inchikey_count, nrow(df_result)))
}