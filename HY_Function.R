#####################################################
#                    空白峰识别                       #
#             *使用时更改参数设置部分路径*               #
#####################################################

Blank_peak <- function(Blank_filepath,Blank_output_dir){
#主程序
#如果输出路径不存在则自动创建设置的输出路径
if (!dir.exists(Blank_output_dir)) {
  dir.create(Blank_output_dir)
}

find_diffs_and_positions <- function(vec) {
  n <- length(vec)
  results <- list()
  for (i in 1:(n - 1)) {
    for (j in (i + 1):n) {
      diff <- vec[j] - vec[i]
      pos1 <- i
      pos2 <- j
      results[[length(results) + 1]] <- list(diff = diff, pos1 = pos1, pos2 = pos2)
    }
  }
  return(results)
}
moveions <- function(features,rttol,mztol){
  order_data <- arrange(features,desc(mz))
  order_data$Designate_PC <- rep(NA,dim(order_data)[1])
  order_data[1,dim(features)[2]+1] = paste("PC",1,sep = "")
  PC_record <- as.data.frame(order_data[1,c("mz","rt","Designate_PC")])

  for (i in 2:length(order_data$Designate_PC)){
    loc <- which(order_data[i,"rt"] >= PC_record$rt - rtTol & order_data[i,"rt"] <= PC_record$rt + rtTol & order_data[i,"mz"] >= PC_record$mz - mzTol & order_data[i,"mz"] <= PC_record$mz + mzTol)
    if(length(loc) >0 ){
      if(length(loc) >1 ){
        mz_rt_diff <- list()
        #添加化学位移和sigma及gamma的误差
        for (j in 1:length(loc)){
          mz_rt_diff[[j]] <- sqrt((order_data[i,"rt"] - PC_record$rt[loc[j]])^2+(order_data[i,"mz"] - PC_record$mz[loc[j]])^2)
        }
        mz_rt_diff_min <- which.min(mz_rt_diff)
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        Update_PC <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        PC_loc <- which(Update_PC==order_data$Designate_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }else{
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc]
        Update_PC <- PC_record$Designate_PC[loc]
        PC_loc <- which(order_data$Designate_PC == Update_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }
    }else{
      n <- length(PC_record$Designate_PC)+1
      order_data$Designate_PC[i] = paste("PC",n,sep = "")
      PC_record <- rbind(PC_record,order_data[i,c("mz","rt","Designate_PC")])
    }
  }

  #以带4个电荷的离子为例进行实验
  index_data_all <- NULL
  for(s in 1:dim(PC_record)[1]){
    pc <- PC_record$Designate_PC[s]
    indpc <- which(order_data$Designate_PC %in% pc)
    data <- order_data[indpc,]
    data <- arrange(data,mz)
    range_mz_diff <- min(data$mz)
    vEc <- data$mz

    if(length(vEc) > 1){
      mz_diff <- find_diffs_and_positions(vEc)

      cDiff <- 1.0034
      #2个电荷的同位素峰之间的理论质荷比差
      if (range_mz_diff > 400){
        Charges <- 2:100
        expThetol <- 0.01
      }else{
        Charges <- 2:5
        expThetol <- 0.004
      }

      ezIndex <- NULL
      for(e in 1:length(Charges)){
        theCharge <- cDiff/Charges[e]
        #暂定多电荷离子同位素质荷比差异的实际值与理论值差异为0.002，作为expThetol
        #循环找出在expThetol范围内的离子的索引
        zIndex <- NULL
        for(z in 1:length(mz_diff)){
          if (mz_diff[[z]]$diff >= theCharge - expThetol & mz_diff[[z]]$diff <= theCharge + expThetol){
            zIndex <- c(zIndex,z)
          }
        }
        ezIndex <- c(ezIndex,zIndex)
      }

      if(length(ezIndex) > 0){
        ind_ez_all <- NULL
        for(ez in 1:length(ezIndex)){
          ind_ez <- ezIndex[ez]
          ind_ez_left <-c(mz_diff[[ind_ez]]$pos1,mz_diff[[ind_ez]]$pos2)
          ind_ez_all <- sort(unique(c(ind_ez_all,ind_ez_left)))
        }
        ezIndex <- sort(unique(ind_ez_all))

        #根据多电荷离子的索引值，取出非多电荷离子的索引
        index_lit <- (1:dim(data)[1])[!((1:dim(data)[1]) %in% ezIndex)]

        if(length(index_lit) > 0){
          index_data <- data[index_lit,]
        }else{
          index_data <- NULL
        }
      }else{
        index_data <- data
      }
    }else{
      index_data <- data
    }
    index_data_all <- rbind(index_data_all,index_data)
  }
  return(index_data_all)
}

#导入包
library(xcms)
library(dplyr)

mzfiles<-list.files(Blank_filepath, recursive=TRUE, full.names=TRUE)
  print(mzfiles)

Blank_start_time <- Sys.time()

#进行空白峰识别
xset<-xcmsSet(mzfiles,method="AMW_SMI",
              ppm=15,
              peakwidth=c(3,10),
              prefilter=c(8,100),
              mzCenterFun="wMean",
              integrate=1,
              mzdiff=-0.001,
              fitgauss=TRUE,
              noise=100,
              sleep=0,
              verboseColumns=TRUE,
              ROI.list=list(),
              firstBaselineCheck=TRUE,
              roiScales=NULL,
              scanrange= numeric(),
              wCoefsthresh=200,
              peakIntensity=500,
              rLLthresh=2,
              rLWthresh=8,
              vLLthresh=2,
              vLWthresh=8,
              peakHeight=500,
              eSNRthresh=6,
              SMIthresh=0.4,
              muthreshp=5,
              hthreshp=0.3,
              sigmathreshp=2,
              pblthreshp=0.3,
              rSMIthresh=0.35,
              gsnthresh=6,
              tfwhm=c(1.5,25))

write.csv(xset@peaks,file.path(paste0(Blank_output_dir,"/POS_KB_peaks.csv")))

Blank_end_time <- Sys.time()
Blank_time_taken <- Blank_end_time - Blank_start_time

cat(sprintf("空白峰识别耗时： %.3f \n", Blank_time_taken))
cat(sprintf("---空白峰识别完成---\n"))
cat(sprintf("---文件保存在%s 目录下---\n", Blank_output_dir))
}

#####################################################
#                  正离子峰识别和匹配                   #
#             *使用时更改参数设置部分路径*               #
#####################################################

POS_peak_match <- function (POS_filepath,POS_output_dir){

find_diffs_and_positions <- function(vec) {
  n <- length(vec)
  results <- list()
  for (i in 1:(n - 1)) {
    for (j in (i + 1):n) {
      diff <- vec[j] - vec[i]
      pos1 <- i
      pos2 <- j
      results[[length(results) + 1]] <- list(diff = diff, pos1 = pos1, pos2 = pos2)
    }
  }
  return(results)
}
moveions <- function(features,rttol,mztol){
  order_data <- arrange(features,desc(mz))
  order_data$Designate_PC <- rep(NA,dim(order_data)[1])
  order_data[1,dim(features)[2]+1] = paste("PC",1,sep = "")
  PC_record <- as.data.frame(order_data[1,c("mz","rt","Designate_PC")])

  for (i in 2:length(order_data$Designate_PC)){
    loc <- which(order_data[i,"rt"] >= PC_record$rt - rtTol & order_data[i,"rt"] <= PC_record$rt + rtTol & order_data[i,"mz"] >= PC_record$mz - mzTol & order_data[i,"mz"] <= PC_record$mz + mzTol)
    if(length(loc) >0 ){
      if(length(loc) >1 ){
        mz_rt_diff <- list()
        #添加化学位移和sigma及gamma的误差
        for (j in 1:length(loc)){
          mz_rt_diff[[j]] <- sqrt((order_data[i,"rt"] - PC_record$rt[loc[j]])^2+(order_data[i,"mz"] - PC_record$mz[loc[j]])^2)
        }
        mz_rt_diff_min <- which.min(mz_rt_diff)
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        Update_PC <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        PC_loc <- which(Update_PC==order_data$Designate_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }else{
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc]
        Update_PC <- PC_record$Designate_PC[loc]
        PC_loc <- which(order_data$Designate_PC == Update_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }
    }else{
      n <- length(PC_record$Designate_PC)+1
      order_data$Designate_PC[i] = paste("PC",n,sep = "")
      PC_record <- rbind(PC_record,order_data[i,c("mz","rt","Designate_PC")])
    }
  }

  #以带4个电荷的离子为例进行实验
  index_data_all <- NULL
  for(s in 1:dim(PC_record)[1]){
    pc <- PC_record$Designate_PC[s]
    indpc <- which(order_data$Designate_PC %in% pc)
    data <- order_data[indpc,]
    data <- arrange(data,mz)
    range_mz_diff <- min(data$mz)
    vEc <- data$mz

    if(length(vEc) > 1){
      mz_diff <- find_diffs_and_positions(vEc)

      cDiff <- 1.0034
      #2个电荷的同位素峰之间的理论质荷比差
      if (range_mz_diff > 400){
        Charges <- 2:100
        expThetol <- 0.01
      }else{
        Charges <- 2:5
        expThetol <- 0.004
      }

      ezIndex <- NULL
      for(e in 1:length(Charges)){
        theCharge <- cDiff/Charges[e]
        #暂定多电荷离子同位素质荷比差异的实际值与理论值差异为0.002，作为expThetol
        #循环找出在expThetol范围内的离子的索引
        zIndex <- NULL
        for(z in 1:length(mz_diff)){
          if (mz_diff[[z]]$diff >= theCharge - expThetol & mz_diff[[z]]$diff <= theCharge + expThetol){
            zIndex <- c(zIndex,z)
          }
        }
        ezIndex <- c(ezIndex,zIndex)
      }

      if(length(ezIndex) > 0){
        ind_ez_all <- NULL
        for(ez in 1:length(ezIndex)){
          ind_ez <- ezIndex[ez]
          ind_ez_left <-c(mz_diff[[ind_ez]]$pos1,mz_diff[[ind_ez]]$pos2)
          ind_ez_all <- sort(unique(c(ind_ez_all,ind_ez_left)))
        }
        ezIndex <- sort(unique(ind_ez_all))

        #根据多电荷离子的索引值，取出非多电荷离子的索引
        index_lit <- (1:dim(data)[1])[!((1:dim(data)[1]) %in% ezIndex)]

        if(length(index_lit) > 0){
          index_data <- data[index_lit,]
        }else{
          index_data <- NULL
        }
      }else{
        index_data <- data
      }
    }else{
      index_data <- data
    }
    index_data_all <- rbind(index_data_all,index_data)
  }
  return(index_data_all)
}

#导入包
library(xcms)
library(dplyr)
#遍历文件夹中正离子的样品数量
sample_files <- list.files(
  path = POS_filepath,
  pattern = "\\.mzML$|\\.mzXML$",  # 匹配常见质谱数据格式
  ignore.case = TRUE,              # 忽略大小写
  full.names = FALSE               # 返回文件名
)

#样品数量 = 文件数量
POS_sample_num <- length(sample_files)

#检查结果
if (POS_sample_num == 0) {
  stop("错误：在文件夹 ", POS_filepath, " 中未找到任何质谱数据文件！")
} else {
  message("找到 ", POS_sample_num, " 个样品文件")
}

if (!dir.exists(POS_output_dir)) {
  dir.create(POS_output_dir)
}

mzfiles<-list.files(POS_filepath, recursive=TRUE, full.names=TRUE)
print(mzfiles)

POS_xset_start_time <- Sys.time()

xset<-xcmsSet(mzfiles,method="AMW_SMI",
              ppm=15,
              peakwidth=c(3,10),
              prefilter=c(8,100),
              mzCenterFun="wMean",
              integrate=1,
              mzdiff=-0.001,
              fitgauss=TRUE,
              noise=100,
              sleep=0,
              verboseColumns=TRUE,
              ROI.list=list(),
              firstBaselineCheck=TRUE,
              roiScales=NULL,
              scanrange= numeric(),
              wCoefsthresh=200,
              peakIntensity=500,
              rLLthresh=2,
              rLWthresh=8,
              vLLthresh=2,
              vLWthresh=8,
              peakHeight=500,
              eSNRthresh=6,
              SMIthresh=0.4,
              muthreshp=5,
              hthreshp=0.3,
              sigmathreshp=2,
              pblthreshp=0.3,
              rSMIthresh=0.35,
              gsnthresh=6,
              tfwhm=c(1.5,25))

tIndex_data_all <- NULL
rtTol <- 3
mzTol <- 1

#循环去除多电荷
for(t in 1:POS_sample_num){
  samind <- which(xset@peaks[,"sample"] == t)
  feature <- xset@peaks[samind,]
  features <- as.data.frame(feature)
  index_data_all <-moveions(features,rtTol,mzTol)
  tIndex_data_all <- rbind(tIndex_data_all,index_data_all)
}

tIndex_data_all <- na.omit(tIndex_data_all)
tIndex_data_all <- tIndex_data_all[,-dim(tIndex_data_all)[2]]

write.csv(tIndex_data_all,file.path(paste0(POS_output_dir,"/01pos_tIndex_data_all_each_sample.csv")))

POS_xset_end_time <- Sys.time()
POS_xset_time_taken <- POS_xset_end_time - POS_xset_start_time

cat(sprintf("正离子峰识别耗时： %.3f \n", POS_xset_time_taken))
cat(sprintf("---正离子峰识别完成---\n"))
cat(sprintf("---文件保存在%s 目录下---\n", POS_output_dir))

POS_peak_match_start_time <- Sys.time()

xset@peaks[1:dim(tIndex_data_all)[1],] <- as.matrix(tIndex_data_all[1:dim(tIndex_data_all)[1],])

#减去去除的多电荷的维度，让类的大小和去除多电荷后的大小一样，数据才能再放回去
xset@peaks <- xset@peaks[-c((dim(tIndex_data_all)[1]+1):dim(xset@peaks)[1]),]

#峰匹配和对齐
xset1 <- group(xset, method = "density", minfrac=0.6, bw=2, mzwid=0.01)
xset2 <- retcor(xset1, smooth="loess", family = "gaussian", span=0.2)
xset3 <- group(xset2, method = "density", minfrac=0.6, bw=1, mzwid=0.01)

#峰补齐  0和10之间的差异
xset4 <- fillPeaks(xset3)
getPeaks_selection <- function(xs, method="medret", value="into") {

  # 检查 xs 是否为 xcmsSet 对象
  if (!"xcmsSet" %in% class(xs)) {
    stop ("Parameter xs is not an xcmsSet object\n")
  }

  # 测试 xcmsSet 是否分组
  if (nrow(xs@groups) > 0 && length(xs@filepaths) > 1) {
    # 获取分组信息
     groupmat <- as.data.frame(xs@groups)
    ts <- data.frame(cbind(groupmat, groupval(xs, method=method, value=value)), row.names = NULL)
    # 重命名列名
    cnames <- colnames(ts)

    if (cnames[1] == 'mzmed') {
      cnames[1] <- 'mz'
    } else {
      stop ('Peak information ?!?')
    }

    if (cnames[4] == 'rtmed') {
      cnames[4] <- 'rt'
    } else {
      stop ('Peak information ?!?')
    }

    colnames(ts) <- cnames
  } else if (length(sampnames(xs)) == 1) {
    ts <- xs@peaks
  } else {
    stop ('First argument must be a xcmsSet with group information or contain only one sample.')
  }

  return(as.matrix(ts))
}
gp2 <- getPeaks_selection(xset4)

write.csv(gp2,file.path(paste0(POS_output_dir,"/<EMAIL>")))

POS_peak_match_end_time <- Sys.time()
POS_peak_match_time_taken <- POS_peak_match_end_time - POS_peak_match_start_time

cat(sprintf("正离子峰匹配和对齐耗时： %.3f \n", POS_peak_match_time_taken))
cat(sprintf("---正离子峰匹配和对齐完成---\n"))
cat(sprintf("---文件保存在%s 目录下---\n", POS_output_dir))
}

#####################################################
#                    正离子同位素识别                  #
#          *使用时更改参数设置正离子峰识别部分路径*        #
#  *正离子输出路径中应包含**************************    #
#####################################################

POS_isotope_match <- function(POS_filepath,POS_output_dir){

find_diffs_and_positions <- function(vec) {
  n <- length(vec)
  results <- list()
  for (i in 1:(n - 1)) {
    for (j in (i + 1):n) {
      diff <- vec[j] - vec[i]
      pos1 <- i
      pos2 <- j
      results[[length(results) + 1]] <- list(diff = diff, pos1 = pos1, pos2 = pos2)
    }
  }
  return(results)
}
moveions <- function(features,rttol,mztol){
  order_data <- arrange(features,desc(mz))
  order_data$Designate_PC <- rep(NA,dim(order_data)[1])
  order_data[1,dim(features)[2]+1] = paste("PC",1,sep = "")
  PC_record <- as.data.frame(order_data[1,c("mz","rt","Designate_PC")])

  for (i in 2:length(order_data$Designate_PC)){
    loc <- which(order_data[i,"rt"] >= PC_record$rt - rtTol & order_data[i,"rt"] <= PC_record$rt + rtTol & order_data[i,"mz"] >= PC_record$mz - mzTol & order_data[i,"mz"] <= PC_record$mz + mzTol)
    if(length(loc) >0 ){
      if(length(loc) >1 ){
        mz_rt_diff <- list()
        #添加化学位移和sigma及gamma的误差
        for (j in 1:length(loc)){
          mz_rt_diff[[j]] <- sqrt((order_data[i,"rt"] - PC_record$rt[loc[j]])^2+(order_data[i,"mz"] - PC_record$mz[loc[j]])^2)
        }
        mz_rt_diff_min <- which.min(mz_rt_diff)
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        Update_PC <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        PC_loc <- which(Update_PC==order_data$Designate_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }else{
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc]
        Update_PC <- PC_record$Designate_PC[loc]
        PC_loc <- which(order_data$Designate_PC == Update_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }
    }else{
      n <- length(PC_record$Designate_PC)+1
      order_data$Designate_PC[i] = paste("PC",n,sep = "")
      PC_record <- rbind(PC_record,order_data[i,c("mz","rt","Designate_PC")])
    }
  }

  #以带4个电荷的离子为例进行实验
  index_data_all <- NULL
  for(s in 1:dim(PC_record)[1]){
    pc <- PC_record$Designate_PC[s]
    indpc <- which(order_data$Designate_PC %in% pc)
    data <- order_data[indpc,]
    data <- arrange(data,mz)
    range_mz_diff <- min(data$mz)
    vEc <- data$mz

    if(length(vEc) > 1){
      mz_diff <- find_diffs_and_positions(vEc)
     #mz_diff <- round(diff(data$mz),4)

      cDiff <- 1.0034
      #2个电荷的同位素峰之间的理论质荷比差
      if (range_mz_diff > 400){
        Charges <- 2:100
        expThetol <- 0.01
      }else{
        Charges <- 2:5
        expThetol <- 0.004
      }

      ezIndex <- NULL
      for(e in 1:length(Charges)){
        theCharge <- cDiff/Charges[e]
        #暂定多电荷离子同位素质荷比差异的实际值与理论值差异为0.002，作为expThetol
        #循环找出在expThetol范围内的离子的索引
        zIndex <- NULL
        for(z in 1:length(mz_diff)){
          if (mz_diff[[z]]$diff >= theCharge - expThetol & mz_diff[[z]]$diff <= theCharge + expThetol){
            zIndex <- c(zIndex,z)
          }
        }
        ezIndex <- c(ezIndex,zIndex)
      }

      if(length(ezIndex) > 0){
        ind_ez_all <- NULL
        for(ez in 1:length(ezIndex)){
          ind_ez <- ezIndex[ez]
          ind_ez_left <-c(mz_diff[[ind_ez]]$pos1,mz_diff[[ind_ez]]$pos2)
          ind_ez_all <- sort(unique(c(ind_ez_all,ind_ez_left)))
        }
        ezIndex <- sort(unique(ind_ez_all))

        #根据多电荷离子的索引值，取出非多电荷离子的索引
        index_lit <- (1:dim(data)[1])[!((1:dim(data)[1]) %in% ezIndex)]

        if(length(index_lit) > 0){
          index_data <- data[index_lit,]
        }else{
          index_data <- NULL
        }
      }else{
        index_data <- data
      }
    }else{
      index_data <- data
    }
    index_data_all <- rbind(index_data_all,index_data)
  }
  return(index_data_all)
}

  if(!dir.exists(POS_output_dir)){
    dir.create(POS_output_dir)
  }

if(!exists(file.path(paste0(POS_output_dir,"/<EMAIL>")))){
  POS_peak_match(POS_filepath,POS_output_dir)
}

index_data_all <- read.csv(file.path(paste0(POS_output_dir,"/<EMAIL>")))

library(readxl)
library(dplyr)
library(tidyr)

POS_isotope_match_start_time <- Sys.time()

pos_group_filtered_sorted <- index_data_all %>% arrange(mz)
pos_group_filtered_sorted$mz <- as.numeric(as.character(pos_group_filtered_sorted$mz))
pos_group_filtered_sorted$rt <- as.numeric(as.character(pos_group_filtered_sorted$rt))

# 初始化分组计数器及 group 列
group_counter <- 1
pos_group_filtered_sorted$isotopes1 <- NA
mz_diff1 <- 1.001
mz_diff2 <- 1.005
rt_diff  <- 0.9
# 创建一个邻接矩阵来存储连接关系
n <- nrow(pos_group_filtered_sorted)
connected <- matrix(FALSE, nrow = n, ncol = n)

# 检查所有行并填充邻接矩阵
for (i in 1:(n-1)) {
  for (j in (i+1):n) {
    mz_diff <- abs(pos_group_filtered_sorted$mz[i] - pos_group_filtered_sorted$mz[j])
    rt_diff_value <- abs(pos_group_filtered_sorted$rt[i] - pos_group_filtered_sorted$rt[j])

    if ((mz_diff >= mz_diff1 && mz_diff <= mz_diff2) && (rt_diff_value <= rt_diff)) {
      connected[i, j] <- TRUE
      connected[j, i] <- TRUE
    }
  }
}

# 用 BFS/DFS 方法为连接的行分组
visited <- rep(FALSE, n)

for (i in 1:n) {
  if (!visited[i]) {
    # 使用栈来实现 DFS
    stack <- c(i)
    visited[i] <- TRUE
    pos_group_filtered_sorted$isotopes1[i] <- group_counter

    # 记录当前组的成员
    current_group_members <- c(i)

    while (length(stack) > 0) {
      current <- stack[length(stack)]
      # 弹出栈顶元素
      stack <- stack[-length(stack)]

      for (j in 1:n) {
        if (connected[current, j] && !visited[j]) {
          visited[j] <- TRUE
          pos_group_filtered_sorted$isotopes1[j] <- group_counter
          # 将新连接的行压入堆栈
          stack <- c(stack, j)
          # 记录当前组的成员
          current_group_members <- c(current_group_members, j)
        }
      }
    }

    # 只有当当前组成员数量大于等于2时，才增加分组计数器
    if (length(current_group_members) >= 2) {
      group_counter <- group_counter + 1
    } else {
      # 如果组成员少于2，可以将这些成员的组编号设为NA（或其他标识）
      pos_group_filtered_sorted$isotopes1[current_group_members] <- NA
    }
  }
}

pos_group_filtered_sorted$mean_3_to_11 <- rowMeans(pos_group_filtered_sorted[, 9:11], na.rm = TRUE)

# 仅对 group 不为空的行进行操作  计算每个 group 的数量 b
group_counts <- pos_group_filtered_sorted %>%
  filter(!is.na(isotopes1)) %>%
  group_by(isotopes1) %>% summarize(b = n(), .groups = 'drop')

mean_values <- pos_group_filtered_sorted %>%
  # 仅对 group 不为空的行进行操作
  filter(!is.na(isotopes1)) %>%
  select(isotopes1, mean_3_to_11)

result <- left_join(group_counts, mean_values, by = "isotopes1")
# 添加方括号到group列
result <- result %>%mutate(isotopes1 = paste0("[", isotopes1, "]"))
# 在每个 group 内部进行排序
result <- result %>%
  # 先按组分组
  group_by(isotopes1) %>%
  # 按 mean_3_to_11 降序排列，.by_group = TRUE 确保这是在组内排序
  arrange(desc(mean_3_to_11), .by_group = TRUE)
#将[1]和[M]+连接
result <- result %>% mutate(label = ifelse(row_number() == 1, "[M]+", paste0("[M+", row_number() - 1, "]+")))
pos_group_filtered_sorted$label <- NA

# 根据 mean_3_to_11 匹配并填充 label
for (i in 1:nrow(result)) {
  mean_value <- result$mean_3_to_11[i]
  label_value <- result$label[i]
  pos_group_filtered_sorted$label[pos_group_filtered_sorted$mean_3_to_11 == mean_value] <- label_value  # 找到在 pos_group_filtered_sorted 中对应的 mean_3_to_11 行
}

pos_group_filtered_sorted <- pos_group_filtered_sorted %>%
  mutate(isotopes1 = paste0("[", isotopes1, "] ", label)) %>%
  select(-mean_3_to_11, -label)

# 将内容为 NA 的行变为空字符串
pos_group_filtered_sorted <- pos_group_filtered_sorted %>%
  # 清除包含 "[NA]" 的部分
  mutate(isotopes1 = gsub("\\[NA\\].*", "", isotopes1)) %>%
  # 去掉前后空格
  mutate(isotopes1 = trimws(isotopes1)) %>%
  # 清除空字符串
  mutate(isotopes1 = ifelse(isotopes1 == "", "", isotopes1))

write.csv(pos_group_filtered_sorted, file.path(paste0(POS_output_dir,"/03pos_group_filtered_sorted_with_new_iso1.csv")), row.names = FALSE)

POS_isotope_match_end_time <- Sys.time()
POS_isotope_match_time_taken <- POS_isotope_match_end_time - POS_isotope_match_start_time

cat(sprintf("正离子同位素识别耗时： %.3f \n", POS_isotope_match_time_taken))
cat(sprintf("---正离子同位素识别完成---\n"))
cat(sprintf("---文件保存在%s 目录下---\n", POS_output_dir))
}

#####################################################
#                  负离子峰识别和匹配                   #
#             *使用时更改参数设置部分路径*               #
#####################################################

NEG_peak_match <- function(NEG_filepath,NEG_output_dir){
library(xcms)
library(dplyr)

find_diffs_and_positions <- function(vec) {
  n <- length(vec)
  results <- list()
  for (i in 1:(n - 1)) {
    for (j in (i + 1):n) {
      diff <- vec[j] - vec[i]
      pos1 <- i
      pos2 <- j
      results[[length(results) + 1]] <- list(diff = diff, pos1 = pos1, pos2 = pos2)
    }
  }
  return(results)
}
moveions <- function(features,rttol,mztol){
  order_data <- arrange(features,desc(mz))
  order_data$Designate_PC <- rep(NA,dim(order_data)[1])
  order_data[1,dim(features)[2]+1] = paste("PC",1,sep = "")
  PC_record <- as.data.frame(order_data[1,c("mz","rt","Designate_PC")])

  for (i in 2:length(order_data$Designate_PC)){
    loc <- which(order_data[i,"rt"] >= PC_record$rt - rtTol & order_data[i,"rt"] <= PC_record$rt + rtTol & order_data[i,"mz"] >= PC_record$mz - mzTol & order_data[i,"mz"] <= PC_record$mz + mzTol)
    if(length(loc) >0 ){
      if(length(loc) >1 ){
        mz_rt_diff <- list()
        #添加化学位移和sigma及gamma的误差
        for (j in 1:length(loc)){
          mz_rt_diff[[j]] <- sqrt((order_data[i,"rt"] - PC_record$rt[loc[j]])^2+(order_data[i,"mz"] - PC_record$mz[loc[j]])^2)
        }
        mz_rt_diff_min <- which.min(mz_rt_diff)
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        Update_PC <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        PC_loc <- which(Update_PC==order_data$Designate_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }else{
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc]
        Update_PC <- PC_record$Designate_PC[loc]
        PC_loc <- which(order_data$Designate_PC == Update_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }
    }else{
      n <- length(PC_record$Designate_PC)+1
      order_data$Designate_PC[i] = paste("PC",n,sep = "")
      PC_record <- rbind(PC_record,order_data[i,c("mz","rt","Designate_PC")])
    }
  }

  #以带4个电荷的离子为例进行实验
  index_data_all <- NULL
  for(s in 1:dim(PC_record)[1]){
    pc <- PC_record$Designate_PC[s]
    indpc <- which(order_data$Designate_PC %in% pc)
    data <- order_data[indpc,]
    data <- arrange(data,mz)
    range_mz_diff <- min(data$mz)
    vEc <- data$mz

    if(length(vEc) > 1){
      mz_diff <- find_diffs_and_positions(vEc)

      cDiff <- 1.0034
      #2个电荷的同位素峰之间的理论质荷比差
      if (range_mz_diff > 400){
        Charges <- 2:100
        expThetol <- 0.01
      }else{
        Charges <- 2:5
        expThetol <- 0.004
      }

      ezIndex <- NULL
      for(e in 1:length(Charges)){
        theCharge <- cDiff/Charges[e]
        #暂定多电荷离子同位素质荷比差异的实际值与理论值差异为0.002，作为expThetol
        #循环找出在expThetol范围内的离子的索引
        zIndex <- NULL
        for(z in 1:length(mz_diff)){
          if (mz_diff[[z]]$diff >= theCharge - expThetol & mz_diff[[z]]$diff <= theCharge + expThetol){
            zIndex <- c(zIndex,z)
          }
        }
        ezIndex <- c(ezIndex,zIndex)
      }

      if(length(ezIndex) > 0){
        ind_ez_all <- NULL
        for(ez in 1:length(ezIndex)){
          ind_ez <- ezIndex[ez]
          ind_ez_left <-c(mz_diff[[ind_ez]]$pos1,mz_diff[[ind_ez]]$pos2)
          ind_ez_all <- sort(unique(c(ind_ez_all,ind_ez_left)))
        }
        ezIndex <- sort(unique(ind_ez_all))

        #根据多电荷离子的索引值，取出非多电荷离子的索引
        index_lit <- (1:dim(data)[1])[!((1:dim(data)[1]) %in% ezIndex)]

        if(length(index_lit) > 0){
          index_data <- data[index_lit,]
        }else{
          index_data <- NULL
        }
      }else{
        index_data <- data
      }
    }else{
      index_data <- data
    }
    index_data_all <- rbind(index_data_all,index_data)
  }
  return(index_data_all)
}

NEG_peak_match_start_time <- Sys.time()

#遍历文件夹中负离子的样品数量
sample_files <- list.files(
  path = NEG_filepath,
  pattern = "\\.mzML$|\\.mzXML$",  # 匹配常见质谱数据格式
  ignore.case = TRUE,              # 忽略大小写
  full.names = FALSE               # 返回文件名
)

#样品数量 = 文件数量
NEG_sample_num <- length(sample_files)

#检查结果
if (NEG_sample_num == 0) {
  stop("错误：在文件夹 ", NEG_filepath, " 中未找到任何质谱数据文件！")
} else {
  message("找到 ", NEG_sample_num, " 个样品文件")
}

if(!dir.exists(NEG_output_dir)){
  dir.create(NEG_output_dir)
}

mzfiles<-list.files(NEG_filepath, recursive=TRUE, full.names=TRUE)
mzfiles

xset<-xcmsSet(mzfiles,method="AMW_SMI",
              ppm=15,
              peakwidth=c(3,10),
              prefilter=c(8,100),
              mzCenterFun="wMean",
              integrate=1, mzdiff=-0.001,
              fitgauss=TRUE,  noise=100,
              sleep=0,
              verboseColumns=TRUE,
              ROI.list=list(), firstBaselineCheck=TRUE, roiScales=NULL,scanrange= numeric(), wCoefsthresh=200,
              peakIntensity=500,rLLthresh=2,rLWthresh=8,vLLthresh=2,vLWthresh=8,peakHeight=500,eSNRthresh=6,SMIthresh=0.4,
              muthreshp=5,hthreshp=0.3,sigmathreshp=2,pblthreshp=0.3,rSMIthresh=0.35,gsnthresh=6,tfwhm=c(1.5,25))

tIndex_data_all <- NULL
rtTol <- 3
mzTol <- 1

#循环去除多电荷
for(t in 1:NEG_sample_num){
  samind <- which(xset@peaks[,"sample"] == t)
  feature <- xset@peaks[samind,]
  features <- as.data.frame(feature)
  index_data_all <-moveions(features,rtTol,mzTol)
  tIndex_data_all <- rbind(tIndex_data_all,index_data_all)
}

tIndex_data_all <- na.omit(tIndex_data_all)
tIndex_data_all <- tIndex_data_all[,-dim(tIndex_data_all)[2]]

write.csv(tIndex_data_all,file.path(paste0(NEG_output_dir,"/01neg_tIndex_data_all_each_sample.csv")))

xset@peaks[1:dim(tIndex_data_all)[1],] <- as.matrix(tIndex_data_all[1:dim(tIndex_data_all)[1],])
#减去去除的多电荷的维度，让类的大小和去除多电荷后的大小一样，数据才能再放回去
xset@peaks <- xset@peaks[-c((dim(tIndex_data_all)[1]+1):dim(xset@peaks)[1]),]

#峰匹配和对齐
xset1 <- group(xset, method = "density", minfrac=0.6, bw=2, mzwid=0.01)
xset2 <- retcor(xset1, smooth="loess", family = "gaussian", span=0.2 )
xset3 <- group(xset2, method = "density", minfrac=0.6, bw=1, mzwid=0.01)
#峰补齐  0和10之间的差异
xset4 <- fillPeaks(xset3)
getPeaks_selection <- function(xs, method="medret", value="into") {

  # 检查 xs 是否为 xcmsSet 对象
  if (!"xcmsSet" %in% class(xs)) {
    stop ("Parameter xs is not an xcmsSet object\n")
  }

  # 测试 xcmsSet 是否分组
  if (nrow(xs@groups) > 0 && length(xs@filepaths) > 1) {
    # 获取分组信息
    groupmat <- as.data.frame(xs@groups)
    # 生成 peaktable 的数据框
    ts <- data.frame(cbind(groupmat, groupval(xs, method=method, value=value)), row.names = NULL)
    # 重命名列名
    cnames <- colnames(ts)

    if (cnames[1] == 'mzmed') {
      cnames[1] <- 'mz'
    } else {
      stop ('Peak information ?!?')
    }

    if (cnames[4] == 'rtmed') {
      cnames[4] <- 'rt'
    } else {
      stop ('Peak information ?!?')
    }

    colnames(ts) <- cnames
  } else if (length(sampnames(xs)) == 1) {
    # 如果只有一个样本，直接使用原始的峰信息
    ts <- xs@peaks
  } else {
    stop ('First argument must be a xcmsSet with group information or contain only one sample.')
  }

  return(as.matrix(ts))
}
gp2 <- getPeaks_selection(xset4)

write.csv(gp2,file.path(paste0(NEG_output_dir,"/<EMAIL>")))

NEG_peak_match_end_time <- Sys.time()
NEG_peak_match_time_taken <- NEG_peak_match_end_time - NEG_peak_match_start_time

cat(sprintf("负离子峰识别耗时： %.3f 分\n", NEG_peak_match_time_taken))
cat(sprintf("---负离子峰识别完成---\n"))
cat(sprintf("---文件保存在%s 目录下---\n", NEG_output_dir))
}

#####################################################
#                    负离子同位素识别                  #
#          *使用时更改参数设置负离子峰识别部分路径*        #
#  *负离子输出路径中应包含**************************    #
#####################################################

NEG_isotope_match <- function(NEG_filepath,NEG_output_dir){
library(readxl)
library(dplyr)
library(tidyr)

find_diffs_and_positions <- function(vec) {
  n <- length(vec)
  results <- list()
  for (i in 1:(n - 1)) {
    for (j in (i + 1):n) {
      diff <- vec[j] - vec[i]
      pos1 <- i
      pos2 <- j
      results[[length(results) + 1]] <- list(diff = diff, pos1 = pos1, pos2 = pos2)
    }
  }
  return(results)
}
moveions <- function(features,rttol,mztol){
  order_data <- arrange(features,desc(mz))
  order_data$Designate_PC <- rep(NA,dim(order_data)[1])
  order_data[1,dim(features)[2]+1] = paste("PC",1,sep = "")
  PC_record <- as.data.frame(order_data[1,c("mz","rt","Designate_PC")])

  for (i in 2:length(order_data$Designate_PC)){
    loc <- which(order_data[i,"rt"] >= PC_record$rt - rtTol & order_data[i,"rt"] <= PC_record$rt + rtTol & order_data[i,"mz"] >= PC_record$mz - mzTol & order_data[i,"mz"] <= PC_record$mz + mzTol)
    if(length(loc) >0 ){
      if(length(loc) >1 ){
        mz_rt_diff <- list()
        #添加化学位移和sigma及gamma的误差
        for (j in 1:length(loc)){
          mz_rt_diff[[j]] <- sqrt((order_data[i,"rt"] - PC_record$rt[loc[j]])^2+(order_data[i,"mz"] - PC_record$mz[loc[j]])^2)
        }
        mz_rt_diff_min <- which.min(mz_rt_diff)
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        Update_PC <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        PC_loc <- which(Update_PC==order_data$Designate_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }else{
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc]
        Update_PC <- PC_record$Designate_PC[loc]
        PC_loc <- which(order_data$Designate_PC == Update_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }
    }else{
      n <- length(PC_record$Designate_PC)+1
      order_data$Designate_PC[i] = paste("PC",n,sep = "")
      PC_record <- rbind(PC_record,order_data[i,c("mz","rt","Designate_PC")])
    }
  }

  #以带4个电荷的离子为例进行实验
  index_data_all <- NULL
  for(s in 1:dim(PC_record)[1]){
    pc <- PC_record$Designate_PC[s]
    indpc <- which(order_data$Designate_PC %in% pc)
    data <- order_data[indpc,]
    data <- arrange(data,mz)
    range_mz_diff <- min(data$mz)
    vEc <- data$mz

    if(length(vEc) > 1){
      mz_diff <- find_diffs_and_positions(vEc)
     #mz_diff <- round(diff(data$mz),4)

      cDiff <- 1.0034
      #2个电荷的同位素峰之间的理论质荷比差
      if (range_mz_diff > 400){
        Charges <- 2:100
        expThetol <- 0.01
      }else{
        Charges <- 2:5
        expThetol <- 0.004
      }

      ezIndex <- NULL
      for(e in 1:length(Charges)){
        theCharge <- cDiff/Charges[e]
        #暂定多电荷离子同位素质荷比差异的实际值与理论值差异为0.002，作为expThetol
        #循环找出在expThetol范围内的离子的索引
        zIndex <- NULL
        for(z in 1:length(mz_diff)){
          if (mz_diff[[z]]$diff >= theCharge - expThetol & mz_diff[[z]]$diff <= theCharge + expThetol){
            zIndex <- c(zIndex,z)
          }
        }
        ezIndex <- c(ezIndex,zIndex)
      }

      if(length(ezIndex) > 0){
        ind_ez_all <- NULL
        for(ez in 1:length(ezIndex)){
          ind_ez <- ezIndex[ez]
          ind_ez_left <-c(mz_diff[[ind_ez]]$pos1,mz_diff[[ind_ez]]$pos2)
          ind_ez_all <- sort(unique(c(ind_ez_all,ind_ez_left)))
        }
        ezIndex <- sort(unique(ind_ez_all))

        #根据多电荷离子的索引值，取出非多电荷离子的索引
        index_lit <- (1:dim(data)[1])[!((1:dim(data)[1]) %in% ezIndex)]

        if(length(index_lit) > 0){
          index_data <- data[index_lit,]
        }else{
          index_data <- NULL
        }
      }else{
        index_data <- data
      }
    }else{
      index_data <- data
    }
    index_data_all <- rbind(index_data_all,index_data)
  }
  return(index_data_all)
}

  if(!dir.exists(NEG_output_dir)){
    dir.create(NEG_output_dir)
  }

if(!exists(file.path(paste0(NEG_output_dir,"/<EMAIL>")))){
  NEG_peak_match(NEG_filepath,NEG_output_dir)
}


index_data_all <- read.csv(file.path(paste0(NEG_output_dir,"/<EMAIL>")))

neg_group_filtered_sorted <- index_data_all %>% arrange(mz)
neg_group_filtered_sorted$mz <- as.numeric(as.character(neg_group_filtered_sorted$mz))
neg_group_filtered_sorted$rt <- as.numeric(as.character(neg_group_filtered_sorted$rt))

# 初始化分组计数器及 group 列
group_counter <- 1
neg_group_filtered_sorted$isotopes1 <- NA
mz_diff1 <- 1.001
mz_diff2 <- 1.005
rt_diff  <- 0.9

# 创建一个邻接矩阵来存储连接关系
n <- nrow(neg_group_filtered_sorted)
connected <- matrix(FALSE, nrow = n, ncol = n)

# 检查所有行并填充邻接矩阵
for (i in 1:(n-1)) {
  for (j in (i+1):n) {
    mz_diff <- abs(neg_group_filtered_sorted$mz[i] - neg_group_filtered_sorted$mz[j])
    rt_diff_value <- abs(neg_group_filtered_sorted$rt[i] - neg_group_filtered_sorted$rt[j])

    if ((mz_diff >= mz_diff1 && mz_diff <= mz_diff2) && (rt_diff_value <= rt_diff)) {
      connected[i, j] <- TRUE
      connected[j, i] <- TRUE
    }
  }
}

# 用 BFS/DFS 方法为连接的行分组
visited <- rep(FALSE, n)

for (i in 1:n) {
  if (!visited[i]) {
    # 使用栈来实现 DFS
    stack <- c(i)
    visited[i] <- TRUE
    neg_group_filtered_sorted$isotopes1[i] <- group_counter

    # 记录当前组的成员
    current_group_members <- c(i)

    while (length(stack) > 0) {
      current <- stack[length(stack)]
      # 弹出栈顶元素
      stack <- stack[-length(stack)]

      for (j in 1:n) {
        if (connected[current, j] && !visited[j]) {
          visited[j] <- TRUE
          neg_group_filtered_sorted$isotopes1[j] <- group_counter
          # 将新连接的行压入堆栈
          stack <- c(stack, j)
          # 记录当前组的成员
          current_group_members <- c(current_group_members, j)
        }
      }
    }

    # 只有当当前组成员数量大于等于2时，才增加分组计数器
    if (length(current_group_members) >= 2) {
      group_counter <- group_counter + 1
    } else {
      # 如果组成员少于2，可以将这些成员的组编号设为NA（或其他标识）
      neg_group_filtered_sorted$isotopes1[current_group_members] <- NA
    }
  }
}

neg_group_filtered_sorted$mean_3_to_11 <- rowMeans(neg_group_filtered_sorted[, 9:11], na.rm = TRUE)

# 仅对 group 不为空的行进行操作  计算每个 group 的数量 b
group_counts <- neg_group_filtered_sorted %>%
  filter(!is.na(isotopes1)) %>%
  group_by(isotopes1) %>% summarize(b = n(), .groups = 'drop')

mean_values <- neg_group_filtered_sorted %>%
  # 仅对 group 不为空的行进行操作
  filter(!is.na(isotopes1)) %>%
  select(isotopes1, mean_3_to_11)

result <- left_join(group_counts, mean_values, by = "isotopes1")
# 添加方括号到group列
result <- result %>%mutate(isotopes1 = paste0("[", isotopes1, "]"))
# 在每个 group 内部进行排序
result <- result %>%
  # 先按组分组
  group_by(isotopes1) %>%
  # 按 mean_3_to_11 降序排列，.by_group = TRUE 确保这是在组内排序
  arrange(desc(mean_3_to_11), .by_group = TRUE)
#将[1]和[M]+连接
result <- result %>% mutate(label = ifelse(row_number() == 1, "[M]+", paste0("[M+", row_number() - 1, "]+")))
neg_group_filtered_sorted$label <- NA

# 根据 mean_3_to_11 匹配并填充 label
for (i in 1:nrow(result)) {
  mean_value <- result$mean_3_to_11[i]
  label_value <- result$label[i]
  neg_group_filtered_sorted$label[neg_group_filtered_sorted$mean_3_to_11 == mean_value] <- label_value  # 找到在 neg_group_filtered_sorted 中对应的 mean_3_to_11 行
}

neg_group_filtered_sorted <- neg_group_filtered_sorted %>%
  mutate(isotopes1 = paste0("[", isotopes1, "] ", label)) %>%
  select(-mean_3_to_11, -label)

# 将内容为 NA 的行变为空字符串
neg_group_filtered_sorted <- neg_group_filtered_sorted %>%
  # 清除包含 "[NA]" 的部分
  mutate(isotopes1 = gsub("\\[NA\\].*", "", isotopes1)) %>%
  # 去掉前后空格
  mutate(isotopes1 = trimws(isotopes1)) %>%
  # 清除空字符串
  mutate(isotopes1 = ifelse(isotopes1 == "", "", isotopes1))

write.csv(neg_group_filtered_sorted, file.path(paste0(NEG_output_dir,"/03neg_group_filtered_sorted_with_new_iso.csv")), row.names = FALSE)
}

##########################################################################
#                                正负注释                                  #-
#                         *使用时更改参数设置路径*                            #
#   *输出路径中应包含*************************和**************************   #
##########################################################################

POS_NEG_Notes <- function(POS_filepath,POS_output_dir,NEG_filepath,NEG_output_dir,Notes_dir,output_dir){
library(readxl)
library(dplyr)

POS_NEG_Notes_start_time <- Sys.time()

if(!dir.exists(output_dir)){
    dir.create(output_dir)
}

  if(!exists(file.path(paste0(POS_output_dir,"/03pos_group_filtered_sorted_with_new_iso.csv"))) && !exists(file.path(paste0(NEG_output_dir,"/03neg_group_filtered_sorted_with_new_iso.csv")))){
    POS_isotope_match(POS_filepath,POS_output_dir)
    NEG_isotope_match(NEG_filepath,NEG_output_dir)
  }
  else if(!exists(file.path(paste0(POS_output_dir,"/03pos_group_filtered_sorted_with_new_iso.csv")))){
    POS_isotope_match(POS_filepath,POS_output_dir)
  }
  else if(!exists(file.path(paste0(NEG_output_dir,"/03neg_group_filtered_sorted_with_new_iso.csv")))){
    NEG_isotope_match(NEG_filepath,NEG_output_dir)
  }

  find_diffs_and_positions <- function(vec) {
  n <- length(vec)
  results <- list()
  for (i in 1:(n - 1)) {
    for (j in (i + 1):n) {
      diff <- vec[j] - vec[i]
      pos1 <- i
      pos2 <- j
      results[[length(results) + 1]] <- list(diff = diff, pos1 = pos1, pos2 = pos2)
    }
  }
  return(results)
}
moveions <- function(features,rttol,mztol){
  order_data <- arrange(features,desc(mz))
  order_data$Designate_PC <- rep(NA,dim(order_data)[1])
  order_data[1,dim(features)[2]+1] = paste("PC",1,sep = "")
  PC_record <- as.data.frame(order_data[1,c("mz","rt","Designate_PC")])

  for (i in 2:length(order_data$Designate_PC)){
    loc <- which(order_data[i,"rt"] >= PC_record$rt - rtTol & order_data[i,"rt"] <= PC_record$rt + rtTol & order_data[i,"mz"] >= PC_record$mz - mzTol & order_data[i,"mz"] <= PC_record$mz + mzTol)
    if(length(loc) >0 ){
      if(length(loc) >1 ){
        mz_rt_diff <- list()
        #添加化学位移和sigma及gamma的误差
        for (j in 1:length(loc)){
          mz_rt_diff[[j]] <- sqrt((order_data[i,"rt"] - PC_record$rt[loc[j]])^2+(order_data[i,"mz"] - PC_record$mz[loc[j]])^2)
        }
        mz_rt_diff_min <- which.min(mz_rt_diff)
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        Update_PC <- PC_record$Designate_PC[loc][mz_rt_diff_min]
        PC_loc <- which(Update_PC==order_data$Designate_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }else{
        order_data$Designate_PC[i] <- PC_record$Designate_PC[loc]
        Update_PC <- PC_record$Designate_PC[loc]
        PC_loc <- which(order_data$Designate_PC == Update_PC)
        H_rt <- order_data[PC_loc,"rt"]
        H_mz <- order_data[PC_loc,"mz"]
        PC_record$rt[loc] <- H_rt[length(PC_loc)]
        PC_record$mz[loc] <- H_mz[length(PC_loc)]
      }
    }else{
      n <- length(PC_record$Designate_PC)+1
      order_data$Designate_PC[i] = paste("PC",n,sep = "")
      PC_record <- rbind(PC_record,order_data[i,c("mz","rt","Designate_PC")])
    }
  }

  #以带4个电荷的离子为例进行实验
  index_data_all <- NULL
  for(s in 1:dim(PC_record)[1]){
    pc <- PC_record$Designate_PC[s]
    indpc <- which(order_data$Designate_PC %in% pc)
    data <- order_data[indpc,]
    data <- arrange(data,mz)
    range_mz_diff <- min(data$mz)
    vEc <- data$mz

    if(length(vEc) > 1){
      mz_diff <- find_diffs_and_positions(vEc)

      cDiff <- 1.0034
      #2个电荷的同位素峰之间的理论质荷比差
      if (range_mz_diff > 400){
        Charges <- 2:100
        expThetol <- 0.01
      }else{
        Charges <- 2:5
        expThetol <- 0.004
      }

      ezIndex <- NULL
      for(e in 1:length(Charges)){
        theCharge <- cDiff/Charges[e]
        #暂定多电荷离子同位素质荷比差异的实际值与理论值差异为0.002，作为expThetol
        #循环找出在expThetol范围内的离子的索引
        zIndex <- NULL
        for(z in 1:length(mz_diff)){
          if (mz_diff[[z]]$diff >= theCharge - expThetol & mz_diff[[z]]$diff <= theCharge + expThetol){
            zIndex <- c(zIndex,z)
          }
        }
        ezIndex <- c(ezIndex,zIndex)
      }

      if(length(ezIndex) > 0){
        ind_ez_all <- NULL
        for(ez in 1:length(ezIndex)){
          ind_ez <- ezIndex[ez]
          ind_ez_left <-c(mz_diff[[ind_ez]]$pos1,mz_diff[[ind_ez]]$pos2)
          ind_ez_all <- sort(unique(c(ind_ez_all,ind_ez_left)))
        }
        ezIndex <- sort(unique(ind_ez_all))

        #根据多电荷离子的索引值，取出非多电荷离子的索引
        index_lit <- (1:dim(data)[1])[!((1:dim(data)[1]) %in% ezIndex)]

        if(length(index_lit) > 0){
          index_data <- data[index_lit,]
        }else{
          index_data <- NULL
        }
      }else{
        index_data <- data
      }
    }else{
      index_data <- data
    }
    index_data_all <- rbind(index_data_all,index_data)
  }
  return(index_data_all)
}

  if(!exists(file.path(paste0(POS_filepath,"/03pos_group_filtered_sorted_with_new_iso.csv")))){
      pklt_pos <- read.csv(file.path(paste0(POS_output_dir,"/03pos_group_filtered_sorted_with_new_iso.csv")),row.names = 1)
      pklt_neg <- read.csv(file.path(paste0(NEG_output_dir,"/03neg_group_filtered_sorted_with_new_iso.csv")),row.names = 1)
  }else{
      pklt_pos <- read.csv(file.path(paste0(POS_filepath,"/03pos_group_filtered_sorted_with_new_iso.csv")),row.names = 1)
      pklt_neg <- read.csv(file.path(paste0(NEG_filepath,"/03neg_group_filtered_sorted_with_new_iso.csv")),row.names = 1)
  }

  message("---开始正负注释---")
pklt_pos <- pklt_pos %>% mutate(sample = "pos")
pklt_neg <- pklt_neg %>% mutate(sample = "neg")
colnames(pklt_pos) <- colnames(pklt_neg)

# 定义一个函数来提取第一个中括号之间的内容
extract_first_bracket <- function(text) {
  # 使用正则表达式匹配第一个中括号之间的内容
  match <- regmatches(text, regexpr("\\[(.*?)\\]", text))
  # 如果匹配成功，返回匹配的内容（去掉中括号），否则返回NA
  if (length(match) > 0 && nchar(match) > 2) {
    return(gsub("\\[|\\]", "", match))
  } else {
    return(NA)
  }
}

pos_num <- as.numeric(sapply(pklt_pos$isotopes1, extract_first_bracket))
aaa <- na.omit(pos_num)
pos_maX_num <- max(aaa)

neg_num <- as.numeric(sapply(pklt_neg$isotopes1, extract_first_bracket))
aaa <- na.omit(neg_num)
a_unique <- unique(aaa)
neg_new_num <- 1:length(a_unique)+pos_maX_num

neg_old_new_num <- data.frame(
  # 第一列的数据
  old_num = a_unique,
  # 第二列的数据
  new_num = neg_new_num
)

# 替换序号
tt <- pklt_neg$isotopes1

for (i_neg in (1:length(pklt_neg$isotopes1))) {
  iso_num <- as.numeric(sapply(tt[i_neg], extract_first_bracket))
  nn <- which(iso_num == neg_old_new_num$old_num)
  posi <- gregexpr("]", tt[i_neg])[[1]]
  if (length(nn) >0) {
    iso_new <- paste0("[",neg_old_new_num[nn,2],"]",substr(tt[i_neg], posi + 1, nchar(tt[i_neg])))
  }else {
    iso_new <- NA
  }

  pklt_neg$isotopes1[i_neg] <- iso_new
}

pklt_pos_neg <- bind_rows(pklt_pos, pklt_neg)
pklt_pos_neg <- pklt_pos_neg %>% arrange(mz)

# 定义一个函数来提取第一个中括号之间的内容
extract_first_bracket <- function(text) {
  # 使用正则表达式匹配第一个中括号之间的内容
  match <- regmatches(text, regexpr("\\[(.*?)\\]", text))
  # 如果匹配成功，返回匹配的内容（去掉中括号），否则返回NA
  if (length(match) > 0 && nchar(match) > 2) {
    return(gsub("\\[|\\]", "", match))
  } else {
    return(NA)
  }
}

iso_all <- pklt_pos_neg$isotopes1
# 设置一个预数列填充，num_a是提取到的[]里面的数据，num_b 是对应的行号
num_all <- data.frame(
  num_a = (length(iso_all)+1):(length(iso_all)+length(iso_all)),
  num_b = 1:length(iso_all)
)


# num_all <- numeric(0)  # 初始化一个空向量存储有效数值
for (i_iso in 1:length(iso_all)) {
  # 检查是否是有效内容
  if (!is.na(iso_all[i_iso]) && iso_all[i_iso] != "") {
    # 提取中括号中的内容
    extracted <- extract_first_bracket(iso_all[i_iso])

    # 转换为数值并检查
    if (!is.na(extracted) && nchar(extracted) > 0) {
      numeric_value <- as.numeric(extracted)
      if (!is.na(numeric_value)) {
        num_all$num_a[i_iso] <- numeric_value
      }
    }
  }
}

# 找到不重复元素的索引
unique_indices <- !duplicated(num_all$num_a)
# 使用索引过滤数据框
num_all_unique <- num_all[unique_indices, ]
numbers <- num_all_unique$num_b

row_comb <- t(combn(numbers, 2))

# 开始注释
pklt1_all <- pklt_pos_neg
rules_new <- read_excel(Notes_dir, sheet = "all_adduct")
pp_adduct <- read_excel(Notes_dir, sheet = "pp_adduct")
nn_adduct <- read_excel(Notes_dir, sheet = "nn_adduct")
pn_adduct <- read_excel(Notes_dir, sheet = "pn_adduct")

Adduct_text_all <- data.frame(Column1 = rep(NA, dim(pklt_pos_neg)[1]))
rtTol <- 10
rtTol1<- 3
mzTol <- 0.005
mz_values <- pklt1_all[, c("mz", "sample","rt","isotopes1")]

# 遍历所有行对组合并处理
for (i in 1:nrow(row_comb)) {
  row1 <- row_comb[i, 1]
  row2 <- row_comb[i, 2]
  sample1 <- mz_values$sample[row1]
  sample2 <- mz_values$sample[row2]
  mz1 <- mz_values$mz[row1]
  mz2 <- mz_values$mz[row2]


  #p_p
  if (sample1 == "pos" && sample2 == "pos") {
    mz_diff<-abs(mz1-mz2)
    RT1 <- mz_values$rt[row1]
    RT2 <- mz_values$rt[row2]
    a_rt<- abs(RT1-RT2)
    a_mz <- abs(mz_diff - pp_adduct$massdiff)
    ii <- which(a_mz <= mzTol)

    if (length(ii) > 0 && a_rt<= rtTol1) {
      mz_comb <- c(mz1,mz2)
      max_mz <- which.max(mz_comb)
      min_mz <- which.min(mz_comb)

      exact_matches1 <- rules_new$name == pp_adduct$adduct1[ii]
      nr1 <- which(exact_matches1)
      mdiff1 <- rules_new$massdiff[nr1]
      MZ1 <- mz_comb[max_mz]-mdiff1

      exact_matches2 <- rules_new$name == pp_adduct$adduct2[ii]
      nr2 <- which(exact_matches2)
      mdiff2 <- rules_new$massdiff[nr2]
      MZ2 <- mz_comb[min_mz]-mdiff2

      avg_mz <- round(mean(c(MZ1,MZ2)),3)

      max_adduct <- paste0(pp_adduct[ii,3],avg_mz)
      min_adduct <- paste0(pp_adduct[ii,4],avg_mz)

      if (is.na(Adduct_text_all$Column1[row_comb[i,max_mz]])) {
        Adduct_text_all[row_comb[i,max_mz], ] <- max_adduct
      }else{
        ttt <- paste0(Adduct_text_all$Column1[row_comb[i,max_mz]],"  ", max_adduct)
        split_text <- strsplit(ttt, "  ")[[1]]
        split_text_uni <- unique(split_text)
        combined_string <- paste(split_text_uni, collapse = "  ")
        Adduct_text_all[row_comb[i,max_mz], ] <- combined_string
      }

      if (is.na(Adduct_text_all$Column1[row_comb[i,min_mz]])) {
        Adduct_text_all[row_comb[i,min_mz], ] <- min_adduct
      }else{
        split_text <- strsplit(Adduct_text_all$Column1[row_comb[i,min_mz]], "  ")[[1]]
        split_text_uni <- unique(split_text)
        combined_string <- paste(split_text_uni, collapse = "  ")
        Adduct_text_all[row_comb[i,min_mz], ] <-  combined_string
      }

    }
    #n_n
  } else if (sample1 == "neg" && sample2 == "neg") {
    mz_diff<-abs(mz1-mz2)
    RT1 <- mz_values$rt[row1]
    RT2 <- mz_values$rt[row2]
    a_rt<- abs(RT1-RT2)
    a_mz <- abs(mz_diff - nn_adduct$massdiff)
    ii <- which(a_mz <= mzTol)
    if (length(ii) > 0 && a_rt<= rtTol1 ){
      mz_comb <- c(mz1,mz2)
      max_mz <- which.max(mz_comb)
      min_mz <- which.min(mz_comb)

      exact_matches1 <- rules_new$name == nn_adduct$adduct1[ii]
      nr1 <- which(exact_matches1)
      mdiff1 <- rules_new$massdiff[nr1]
      MZ1 <- mz_comb[max_mz]-mdiff1

      exact_matches2 <- rules_new$name == nn_adduct$adduct2[ii]
      nr2 <- which(exact_matches2)
      mdiff2 <- rules_new$massdiff[nr2]
      MZ2 <- mz_comb[min_mz]-mdiff2

      avg_mz <- round(mean(c(MZ1,MZ2)),3)

      max_adduct <- paste0(nn_adduct[ii,3],avg_mz)
      min_adduct <- paste0(nn_adduct[ii,4],avg_mz)
      Adduct_text_all[row_comb[i,max_mz], ] <- max_adduct
      Adduct_text_all[row_comb[i,min_mz], ] <- min_adduct
    }
    #p_n
  } else if ((sample1 == "pos" && sample2 == "neg") || (sample1 == "neg" && sample2 == "pos")) {
    if (sample1 == "pos" ){
      mz_diff<- mz1-mz2
    }else{
      mz_diff<- mz2-mz1
    }

    RT1 <- mz_values$rt[row1]
    RT2 <- mz_values$rt[row2]
    a_rt<- abs(RT1-RT2)
    a_mz <- abs(mz_diff - pn_adduct$massdiff)
    ii<- which(a_mz <= mzTol)

    if (length(ii) > 0 && a_rt<= rtTol) {
      mz_comb <- c(mz1,mz2)
      sam_comb <- c(sample1,sample2)
      pos_mz <- which(sam_comb == "pos")
      neg_mz <- which(sam_comb == "neg")

      exact_matches1 <- rules_new$name == pn_adduct$adduct1[ii]
      nr1 <- which(exact_matches1)
      mdiff1 <- rules_new$massdiff[nr1]
      MZ1 <- mz_comb[pos_mz]-mdiff1

      exact_matches2 <- rules_new$name == pn_adduct$adduct2[ii]
      nr2 <- which(exact_matches2)
      mdiff2 <- rules_new$massdiff[nr2]
      MZ2 <- mz_comb[neg_mz]-mdiff2

      avg_mz <- round(mean(c(MZ1,MZ2)),3)

      pos_adduct <- paste0(pn_adduct[ii,3],avg_mz)
      neg_adduct <- paste0(pn_adduct[ii,4],avg_mz)

      if (is.na(Adduct_text_all$Column1[row_comb[i,pos_mz]])) {
        Adduct_text_all[row_comb[i,pos_mz], ] <- pos_adduct
      }else{
        ttt <- paste0(Adduct_text_all$Column1[row_comb[i,pos_mz]],"  ", pos_adduct)
        split_text <- strsplit(ttt, "  ")[[1]]
        split_text_uni <- unique(split_text)
        combined_string <- paste(split_text_uni, collapse = "  ")
        Adduct_text_all[row_comb[i,pos_mz], ] <- combined_string
      }

      if (is.na(Adduct_text_all$Column1[row_comb[i,neg_mz]])) {
        Adduct_text_all[row_comb[i,neg_mz], ] <- neg_adduct
      }else{
        split_text <- strsplit(Adduct_text_all$Column1[row_comb[i,neg_mz]], "  ")[[1]]
        split_text_uni <- unique(split_text)
        combined_string <- paste(split_text_uni, collapse = "  ")
        Adduct_text_all[row_comb[i,neg_mz], ] <-  combined_string
      }
    }
  }
}

pklt1_all$adduct <- Adduct_text_all$Column1
pklt1_all$isotopes1 <- pklt_pos_neg$isotopes1

write.csv(pklt1_all, file.path(paste0(output_dir,"/04_pos_neg_adduct.csv")))

#由于匹配过程中会出现mz和rt的权重计算，在pklt1中还是可以看到有多电荷的存在，故二次进行多电荷的去除
library(readxl)
library(dplyr)

features <- pklt1_all
mzdec <- 4; rtdec <- 3;
rtfmt <- paste("%.", rtdec, "f", sep = "")
mzfmt <- paste("%.", mzdec, "f", sep = "")
var1<- paste("nM", sprintf(mzfmt, features[,"mz"]), "T", sprintf(rtfmt, features[,"rt"]), sep = "")
rownames(features) <- var1

index_data_all <- features

write.csv(index_data_all,file.path(paste0(output_dir,"/05_9sample_finddiff_annotation_pklt1.csv")))

POS_NEG_Notes_end_time <- Sys.time()
POS_NEG_Notes_time_taken <- POS_NEG_Notes_end_time - POS_NEG_Notes_start_time

cat(sprintf("正负注释耗时： %.3f \n", POS_NEG_Notes_time_taken))
cat(sprintf("---正负注释完成---\n"))
cat(sprintf("---文件保存在%s 目录下---\n", output_dir))
}

##########################################################################
#                                除去空白                                 #
#                      需要正离子或负离子的峰识别和匹配完成                     #
#                         *使用时更改参数设置路径*                           #
##########################################################################

move_Blank <- function (tIndex_data_all_each_sample_CSV,KB_peaks_CSV,output_dir,mz_tolerance,rt_tolerance){
  if(!dir.exists(output_dir)){
    dir.create(output_dir)
  }

# 读取数据
pos_peaks <- read.csv(tIndex_data_all_each_sample_CSV)
blank_peaks <- read.csv(KB_peaks_CSV)

# 初始化匹配标记
pos_peaks$is_blank <- FALSE

# 对每个正离子峰检查是否与空白峰匹配
for (i in 1:nrow(pos_peaks)) {
  pos_mz <- pos_peaks$mz[i]
  pos_rt <- pos_peaks$rt[i]

  # 检查是否与任何空白峰匹配
  matches <- which(
    abs(blank_peaks$mz - pos_mz) <= mz_tolerance &
    abs(blank_peaks$rt - pos_rt) <= rt_tolerance
  )

  if (length(matches) > 0) {
    pos_peaks$is_blank[i] <- TRUE
  }
}

# 移除匹配的空白峰，保留非空白峰
pos_peaks_filtered <- pos_peaks[!pos_peaks$is_blank, ]
pos_peaks_filtered$is_blank <- NULL

# 保存结果
write.csv(pos_peaks_filtered, file.path(paste0(output_dir,"/01pos_tIndex_data_filtered.csv")), row.names = FALSE)

cat(sprintf("原始峰数量: %d\n", nrow(pos_peaks)))
cat(sprintf("匹配空白峰数量: %d\n", sum(pos_peaks$is_blank)))
cat(sprintf("过滤后峰数量: %d\n", nrow(pos_peaks_filtered)))
}

##########################################################################
#                                识别药物峰                                 #
#                      需要正离子或负离子的峰识别和匹配完成                     #
#                         *使用时更改参数设置路径*                           #
##########################################################################

medicine <- function (add_medicine,none_medicine,output_dir,mz_tolerance,rt_tolerance){

  if(!dir.exists(output_dir)){
    dir.create(output_dir)
  }
  # 读取数据
pos_peaks <- read.csv(add_medicine)
blank_peaks <- read.csv(none_medicine)

# 初始化匹配标记
pos_peaks$is_blank <- FALSE

# 对每个正离子峰检查是否与空白峰匹配
for (i in 1:nrow(pos_peaks)) {
  pos_mz <- pos_peaks$mz[i]
  pos_rt <- pos_peaks$rt[i]

  # 检查是否与任何空白峰匹配
  matches <- which(
    abs(blank_peaks$mz - pos_mz) <= mz_tolerance &
    abs(blank_peaks$rt - pos_rt) <= rt_tolerance
  )

  if (length(matches) > 0) {
    pos_peaks$is_blank[i] <- TRUE
  }
}

# 移除匹配的空白峰，保留非空白峰
pos_peaks_filtered <- pos_peaks[!pos_peaks$is_blank, ]
pos_peaks_filtered$is_blank <- NULL

# 保存结果
write.csv(pos_peaks_filtered, file.path(paste0(output_dir,"/medicine.csv")), row.names = FALSE)

cat(sprintf("原始峰数量: %d\n", nrow(pos_peaks)))
cat(sprintf("匹配空白峰数量: %d\n", sum(pos_peaks$is_blank)))
cat(sprintf("过滤后峰数量: %d\n", nrow(pos_peaks_filtered)))
}

#############################################################################
#                              CSV文件夹批量清理函数                            #
#                     用于清理整个文件夹中所有CSV文件的NAME列,保存单一name          #
#                                clean_folder_csv                           #
#          正则不完整，使用需谨慎   [IIN-based: Match]目前无法匹配                 #
#############################################################################
# 清理化合物名称的核心函数
clean_name <- function(name) {
  # 处理NA值和空字符串
  if (is.na(name) || name == "" || is.null(name)) {
    return(name)
  }

  # 转换为字符串并去除首尾空格
  name <- as.character(name)
  name <- trimws(name)

  # 首先去除引号
  if (grepl('^".*"$', name)) {
    name <- substr(name, 2, nchar(name) - 1)
  }

  # 优先处理规则 - Unknown清理
  if (grepl("Unknown", name, ignore.case = TRUE)) {
    return("")
  }

    # NCGC格式处理 - 保留化学信息
  if (grepl("^NCGC[0-9]+-[0-9]+_", name)) {
    underscore_pos <- regexpr("_", name)[1] + 1
    if (underscore_pos <= nchar(name)) {
      chemical_info <- substr(name, underscore_pos, nchar(name))
      chemical_info <- trimws(chemical_info)
      # 如果下划线后面没有内容或只有空格，则完全清理
      if (chemical_info == "" || nchar(chemical_info) == 0) {
        return("")
      }
      return(chemical_info)
    }
  }

    # [ID]![化合物名] - 去除感叹号及之前的部分
  if (grepl("!", name)) {
    exclamation_pos <- regexpr("!", name)[1] + 1
    # 如果感叹号在字符串末尾，直接清理
    if (exclamation_pos > nchar(name)) {
      return("")
    }
    # 如果感叹号后面有内容，提取并检查
    if (exclamation_pos <= nchar(name)) {
      compound <- substr(name, exclamation_pos, nchar(name))
      compound <- trimws(compound)
      # 如果感叹号后面没有内容或只有空格，则完全清理
      if (compound == "" || nchar(compound) == 0) {
        return("")
      }
      return(compound)
    }
  }

  # 感叹号格式 - 保留化学信息
  if (grepl("!", name)) {
    exclamation_pos <- regexpr("!", name)[1] + 1
    if (exclamation_pos <= nchar(name)) {
      compound <- substr(name, exclamation_pos, nchar(name))
      compound <- trimws(compound)
      # 如果感叹号后面没有内容或只有空格，则完全清理
      if (compound == "" || nchar(compound) == 0) {
        return("")
      }
      return(compound)
    }
  }

  # ID清理规则 - 完全删除
  if (grepl("^[A-Z]*[0-9]{4,}[A-Z]*$", name)) {
    return("")
  }

  if (grepl("^[0-9]+-[0-9]+-[0-9]+$", name)) {
    return("")
  }

  if (grepl("^[A-Z]+-[0-9]+$", name)) {
    return("")
  }

  # 规则: [化合物名] [IIN-based: Match] - 去除IIN-based部分
  if (grepl(" \\[IIN-based: Match\\]$", name)) {
    end_pos <- regexpr(" \\[IIN-based: Match\\]$", name)[1] - 1
    if (end_pos > 0) {
      compound <- substr(name, 1, end_pos)
      return(trimws(compound))
    }
  }

  # 规则1: Spectral Match to [化合物名] from NIST14
  if (grepl("Spectral Match to .+ from NIST14", name)) {
    start_pos <- regexpr("Spectral Match to ", name)[1] + nchar("Spectral Match to ")
    end_pos <- regexpr(" from NIST14", name)[1] - 1
    if (start_pos > 0 && end_pos > start_pos) {
      compound <- substr(name, start_pos, end_pos)
      return(trimws(compound))
    }
  }

  # 规则2: [化合物名] - [数字].0 eV
  if (grepl(" - [0-9]+\\.[0-9]+ eV$", name)) {
    end_pos <- regexpr(" - [0-9]+\\.[0-9]+ eV$", name)[1] - 1
    if (end_pos > 0) {
      compound <- substr(name, 1, end_pos)
      return(trimws(compound))
    }
  }

  # 规则3a: [化合物名]; LC-ESI-ITFT; MS2; CE [数字].0 eV; [M+H]+
  if (grepl("; LC-ESI-.+; MS2; CE [0-9]+\\.[0-9]+ eV; \\[M\\+H\\]\\+$", name)) {
    end_pos <- regexpr("; LC-ESI-", name)[1] - 1
    if (end_pos > 0) {
      compound <- substr(name, 1, end_pos)
      return(trimws(compound))
    }
  }

  # 规则3b: [化合物名]; LC-ESI-ITFT; MS2 (简化版本)
  if (grepl("; LC-ESI-ITFT; MS2$", name)) {
    end_pos <- regexpr("; LC-ESI-ITFT; MS2$", name)[1] - 1
    if (end_pos > 0) {
      compound <- substr(name, 1, end_pos)
      return(trimws(compound))
    }
  }

  # 规则3c: [化合物名]; LC-ESI-ITFT; MS2; CE
  if (grepl("; LC-ESI-ITFT; MS2; CE$", name)) {
    end_pos <- regexpr("; LC-ESI-ITFT; MS2; CE$", name)[1] - 1
    if (end_pos > 0) {
      compound <- substr(name, 1, end_pos)
      return(trimws(compound))
    }
  }

  # 规则4: [化合物名]; CE[数字]; [INCHIKEY]
  if (grepl("; CE[0-9]+; [A-Z\\-]+$", name)) {
    end_pos <- regexpr("; CE[0-9]+;", name)[1] - 1
    if (end_pos > 0) {
      compound <- substr(name, 1, end_pos)
      return(trimws(compound))
    }
  }

  # 规则5: [化合物名]; PlaSMA ID-[数字] - 可能包含(not validated)和Massbank格式
  if (grepl("; PlaSMA ID-[0-9]+$", name)) {
    end_pos <- regexpr("; PlaSMA ID-", name)[1] - 1
    if (end_pos > 0) {
      compound <- substr(name, 1, end_pos)
      compound <- trimws(compound)

      # 检查是否还有(not validated)需要清理
      if (grepl(" \\(not validated\\)$", compound)) {
        not_val_pos <- regexpr(" \\(not validated\\)$", compound)[1] - 1
        if (not_val_pos > 0) {
          compound <- substr(compound, 1, not_val_pos)
          compound <- trimws(compound)
        }
      }

      # 检查是否是Massbank格式
      if (grepl("^Massbank:[^ ]+ ", compound)) {
        start_pos <- regexpr("^Massbank:[^ ]+ ", compound)[1]
        start_pos <- start_pos + attr(regexpr("^Massbank:[^ ]+ ", compound), "match.length")

        remaining <- substr(compound, start_pos, nchar(compound))

        if (grepl("\\|", remaining)) {
          pipe_pos <- regexpr("\\|", remaining)[1] - 1
          compound <- substr(remaining, 1, pipe_pos)
        } else {
          compound <- remaining
        }
        compound <- trimws(compound)
      }

      return(compound)
    }
  }

  # 规则6: [化合物名] (not validated) - 包括Massbank格式的
  if (grepl(" \\(not validated\\)$", name)) {
    end_pos <- regexpr(" \\(not validated\\)$", name)[1] - 1
    if (end_pos > 0) {
      front_part <- substr(name, 1, end_pos)
      front_part <- trimws(front_part)

      if (grepl("^Massbank:[^ ]+ ", front_part)) {
        start_pos <- regexpr("^Massbank:[^ ]+ ", front_part)[1]
        start_pos <- start_pos + attr(regexpr("^Massbank:[^ ]+ ", front_part), "match.length")

        remaining <- substr(front_part, start_pos, nchar(front_part))

        if (grepl("\\|", remaining)) {
          pipe_pos <- regexpr("\\|", remaining)[1] - 1
          compound <- substr(remaining, 1, pipe_pos)
        } else {
          compound <- remaining
        }
        return(trimws(compound))
      } else {
        return(front_part)
      }
    }
  }

  # 规则7a: Massbank: [化合物名]|[其他信息] (没有ID的情况)
  if (grepl("^Massbank: ", name)) {
    start_pos <- regexpr("^Massbank: ", name)[1] + nchar("Massbank: ")

    remaining <- substr(name, start_pos, nchar(name))

    if (grepl("\\|", remaining)) {
      pipe_pos <- regexpr("\\|", remaining)[1] - 1
      compound <- substr(remaining, 1, pipe_pos)
    } else {
      compound <- remaining
    }
    return(trimws(compound))
  }

  # 规则7b: Massbank:[ID] [化合物名]|[其他信息] (有ID的情况)
  if (grepl("^Massbank:[^ ]+ ", name)) {
    start_pos <- regexpr("^Massbank:[^ ]+ ", name)[1]
    start_pos <- start_pos + attr(regexpr("^Massbank:[^ ]+ ", name), "match.length")

    remaining <- substr(name, start_pos, nchar(name))

    if (grepl("\\|", remaining)) {
      pipe_pos <- regexpr("\\|", remaining)[1] - 1
      compound <- substr(remaining, 1, pipe_pos)
    } else {
      compound <- remaining
    }
    return(trimws(compound))
  }

  # 规则8: ReSpect:[ID] [化合物名]|[其他信息]
  if (grepl("^ReSpect:[^ ]+ ", name)) {
    start_pos <- regexpr("^ReSpect:[^ ]+ ", name)[1]
    start_pos <- start_pos + attr(regexpr("^ReSpect:[^ ]+ ", name), "match.length")

    remaining <- substr(name, start_pos, nchar(name))

    if (grepl("\\|", remaining)) {
      pipe_pos <- regexpr("\\|", remaining)[1] - 1
      compound <- substr(remaining, 1, pipe_pos)
    } else {
      compound <- remaining
    }
    return(trimws(compound))
  }

  # 规则8a: MoNA:[ID] [化合物名]|[其他信息]
  if (grepl("^MoNA:[0-9]+ ", name)) {
    start_pos <- regexpr("^MoNA:[0-9]+ ", name)[1]
    start_pos <- start_pos + attr(regexpr("^MoNA:[0-9]+ ", name), "match.length")

    remaining <- substr(name, start_pos, nchar(name))

    if (grepl("\\|", remaining)) {
      pipe_pos <- regexpr("\\|", remaining)[1] - 1
      compound <- substr(remaining, 1, pipe_pos)
    } else {
      compound <- remaining
    }
    return(trimws(compound))
  }

  # 规则8c: MassbankEU:[ID] [化合物名]|[其他信息]
  if (grepl("^MassbankEU:[^ ]+ ", name)) {
    start_pos <- regexpr("^MassbankEU:[^ ]+ ", name)[1]
    start_pos <- start_pos + attr(regexpr("^MassbankEU:[^ ]+ ", name), "match.length")

    remaining <- substr(name, start_pos, nchar(name))

    if (grepl("\\|", remaining)) {
      pipe_pos <- regexpr("\\|", remaining)[1] - 1
      compound <- substr(remaining, 1, pipe_pos)
    } else {
      compound <- remaining
    }
    return(trimws(compound))
  }

  # 规则9a: [化合物名]; LC-ESI-QTOF; MS2; CE
  if (grepl("; LC-ESI-QTOF; MS2; CE$", name)) {
    end_pos <- regexpr("; LC-ESI-QTOF; MS2; CE$", name)[1] - 1
    if (end_pos > 0) {
      compound <- substr(name, 1, end_pos)
      return(trimws(compound))
    }
  }

  # 规则9b: [化合物名]; LC-ESI-QTOF; MS2; [M+H]+; CE
  if (grepl("; LC-ESI-QTOF; MS2; \\[M\\+H\\]\\+; CE$", name)) {
    end_pos <- regexpr("; LC-ESI-QTOF; MS2; \\[M\\+H\\]\\+; CE$", name)[1] - 1
    if (end_pos > 0) {
      compound <- substr(name, 1, end_pos)
      return(trimws(compound))
    }
  }

  # 规则10: [化合物名]; [M+H]+; TToF (CE[数字]CES[数字])
  if (grepl("; \\[M\\+H\\]\\+; TToF \\(CE[0-9]+CES[0-9]+\\)$", name)) {
    end_pos <- regexpr("; \\[M\\+H\\]\\+; TToF", name)[1] - 1
    if (end_pos > 0) {
      compound <- substr(name, 1, end_pos)
      return(trimws(compound))
    }
  }

  # 规则11: 处理其他带有|的情况，通常取第一部分
  if (grepl("\\|", name) && !grepl("^(Massbank:|Massbank: |ReSpect:|MoNA:|MassbankEU:)", name)) {
    pipe_pos <- regexpr("\\|", name)[1] - 1
    compound <- substr(name, 1, pipe_pos)
    return(trimws(compound))
  }

  # 规则12: 去除末尾的INCHIKEY模式 (如果存在分号分隔)
  if (grepl("; ", name)) {
    parts <- strsplit(name, "; ")[[1]]
    if (length(parts) > 1 &&
        grepl("^[A-Z\\-]+$", parts[length(parts)]) &&
        nchar(parts[length(parts)]) >= 14 &&
        grepl("\\-", parts[length(parts)])) {
      compound <- paste(parts[-length(parts)], collapse = "; ")
      return(trimws(compound))
    }
  }

  return(trimws(name))
}

# 清理整个文件夹中所有CSV文件的主函数
clean_folder_csv <- function(input_folder = ".", output_folder = "cleaned_csv_results") {
  cat("开始清理文件夹中的所有CSV文件...\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")

  # 创建输出目录
  if (dir.exists(output_folder)) {
    unlink(output_folder, recursive = TRUE)
  }
  dir.create(output_folder)
  cat("已创建输出目录:", output_folder, "\n")

  # 获取所有CSV文件
  csv_files <- list.files(path = input_folder, pattern = "\\.csv$", full.names = FALSE)

  if (length(csv_files) == 0) {
    cat("文件夹中没有找到CSV文件\n")
    return()
  }

  cat("找到", length(csv_files), "个CSV文件\n\n")

  # 初始化统计变量
  total_files_processed <- 0
  total_records <- 0
  total_changed <- 0

  # 处理每个CSV文件
  for (csv_file in sort(csv_files)) {
    cat("正在处理文件:", csv_file, "\n")

    tryCatch({
      # 读取CSV文件
      input_path <- file.path(input_folder, csv_file)
      df <- read.csv(input_path, stringsAsFactors = FALSE)

      # 检查是否有NAME列
      if (!"NAME" %in% colnames(df)) {
        cat("  警告: 文件", csv_file, "中没有找到NAME列\n")
        next
      }

      # 记录原始数据
      original_names <- df$NAME

      # 清理NAME列
      df$NAME <- sapply(df$NAME, clean_name, USE.NAMES = FALSE)

      # 统计清理结果
      changed_indices <- which(original_names != df$NAME)
      changed_count <- length(changed_indices)
      file_total <- nrow(df)

      cat("  总记录数:", file_total, "\n")
      cat("  已清理记录数:", changed_count, "\n")

      # 生成输出文件路径
      output_path <- file.path(output_folder, csv_file)

      # 保存清理后的文件
      write.csv(df, output_path, row.names = FALSE)
      cat("  文件已保存:", output_path, "\n")

      # 显示清理示例
      if (changed_count > 0) {
        cat("  清理示例:\n")
        n_examples <- min(3, changed_count)
        for (i in 1:n_examples) {
          idx <- changed_indices[i]
          cat("    原始:", original_names[idx], "\n")
          cat("    清理后:", df$NAME[idx], "\n")
          cat("\n")
        }
      }

      # 更新统计
      total_files_processed <- total_files_processed + 1
      total_records <- total_records + file_total
      total_changed <- total_changed + changed_count

    }, error = function(e) {
      cat("  错误: 处理文件", csv_file, "时出现错误:", e$message, "\n")
    })

    cat(paste(rep("-", 40), collapse = ""), "\n")
  }

  # 输出总结
  cat("\n", paste(rep("=", 60), collapse = ""), "\n")
  cat("处理完成！总结:\n")
  cat("- 处理的文件数:", total_files_processed, "\n")
  cat("- 总记录数:", total_records, "\n")
  cat("- 清理的记录数:", total_changed, "\n")
  if (total_records > 0) {
    cat("- 清理率:", sprintf("%.2f%%", (total_changed / total_records) * 100), "\n")
  }
  cat("- 输出目录:", output_folder, "\n")
  cat("\n所有清理后的文件已保存到", output_folder, "目录中！\n")

  # 返回统计信息
  return(list(
    processed_files = total_files_processed,
    total_records = total_records,
    cleaned_records = total_changed,
    cleaning_rate = if(total_records > 0) round((total_changed / total_records) * 100, 2) else 0,
    output_folder = output_folder
  ))
}


#############################################################################
#                              CSV文件夹批量清理函数
#                     #' 合并CSV文件并添加PC组标识
# #' @param pattern 文件匹配模式，默认为"*_sim_results.csv"
# #' @param output_file 输出文件名，默认为"merged_sim_results_with_pc_groups.csv"
# #' @param show_preview 是否显示数据预览，默认为TRUE
# #' @return 返回合并后的数据框
# 只能匹配到*_sim_results\\.csv
#############################################################################
merge_csv_with_pc_groups <- function(input_dir = ".",
                                   output_file = "merged_sim_results_with_pc_groups.csv",
                                   show_preview = TRUE) {

  # 获取指定目录下的所有CSV文件，排除当前脚本文件
  csv_files <- list.files(path = input_dir, pattern = "\\.csv$", full.names = TRUE)

  if (length(csv_files) == 0) {
    stop("在指定目录中未找到匹配的CSV文件！")
  }

  cat("找到", length(csv_files), "个CSV文件\n")

  # 初始化空的数据框列表
  merged_data <- list()

  # 处理每个CSV文件
  for (i in seq_along(csv_files)) {
    tryCatch({
      # 读取CSV文件
      df <- read.csv(csv_files[i], stringsAsFactors = FALSE)

      # 检查并显示列信息
      cat("文件:", basename(csv_files[i]), "列数:", ncol(df), "行数:", nrow(df), "\n")

      # 添加PC group列
      df$PC_Group <- paste0("PC", i)

      # 添加到列表
      merged_data[[i]] <- df

    }, error = function(e) {
      cat("读取文件", csv_files[i], "时出错:", e$message, "\n")
    })
  }

  # 合并所有数据框
  final_df <- bind_rows(merged_data)

  # 创建输出文件的完整路径
  if (!dir.exists(input_dir)) {
    dir.create(input_dir, recursive = TRUE)
  }

  output_path <- file.path(input_dir, output_file)

  # 保存合并后的文件
  write.csv(final_df, file.path(paste0(output_file,"/merged_sim_results_with_pc_groups.csv")), row.names = FALSE)

  cat("\n合并完成！共处理", length(merged_data), "个文件\n")
  cat("输出文件:", output_path, "\n")
  cat("总行数:", nrow(final_df), "\n")
  cat("总列数:", ncol(final_df), "\n")

  # 显示前几行预览
  if (show_preview && nrow(final_df) > 0) {
    cat("\n数据预览:\n")
    print(head(final_df))
  }

  return(final_df)
}

#####################################################
#                     添加pubchem中信息               #
#             *使用时更改参数设置部分路径*               #
#                化合物信息可自定义，列名为NAME          #
#####################################################
Add_pubchem_info <- function(file_path, output_path = NULL,
                             delay = 0.5,
                             force_query_all = FALSE,
                             fields_to_update = c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")) {

  library(httr)
  library(jsonlite)
  library(readxl)
  library(openxlsx)
  library(stringr)

  allowed_fields <- c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")
  fields_to_update <- intersect(fields_to_update, allowed_fields)
  if (length(fields_to_update) == 0) stop("❌ 请至少选择一个有效字段进行查询")

  get_compound_info_from_pubchem <- function(compound_name, max_retries = 3, delay = 1) {
    # 检查输入是否为空或NA
    if (is.na(compound_name) || is.null(compound_name) || nchar(trimws(compound_name)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    name_encoded <- URLencode(compound_name, reserved = TRUE)
    base_url <- "https://pubchem.ncbi.nlm.nih.gov/rest/pug"
    url <- paste0(base_url, "/compound/name/", name_encoded,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    get_valid_smiles <- function(props) {
      if (is.list(props) || is.data.frame(props)) {
        # 尝试获取 SMILES (PubChem实际返回的字段名)
        smiles <- props$SMILES
        if (!is.null(smiles) && length(smiles) >= 1 && !is.na(smiles[1]) && nzchar(as.character(smiles[1]))) {
          return(as.character(smiles[1]))
        }

        # 尝试获取 IsomericSMILES
        iso <- props$IsomericSMILES
        if (!is.null(iso) && length(iso) >= 1 && !is.na(iso[1]) && nzchar(as.character(iso[1]))) {
          return(as.character(iso[1]))
        }

        # 如果没有 IsomericSMILES，尝试 CanonicalSMILES
        cano <- props$CanonicalSMILES
        if (!is.null(cano) && length(cano) >= 1 && !is.na(cano[1]) && nzchar(as.character(cano[1]))) {
          return(as.character(cano[1]))
        }

        # 尝试 ConnectivitySMILES (PubChem的另一个字段名)
        conn <- props$ConnectivitySMILES
        if (!is.null(conn) && length(conn) >= 1 && !is.na(conn[1]) && nzchar(as.character(conn[1]))) {
          return(as.character(conn[1]))
        }
      }
      return(NA)
    }

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        cat(sprintf("❌ 网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          cat(sprintf("❌ JSON解析错误: %s\n", compound_name))
          Sys.sleep(delay * attempt)
          next
        }

        if (is.null(content_json$PropertyTable) || is.null(content_json$PropertyTable$Properties)) {
          cat(sprintf("❌ 未找到化合物信息: %s\n", compound_name))
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        }

        props_df <- content_json$PropertyTable$Properties

        if (nrow(props_df) > 1) {
          cat(sprintf("⚠️ %s 返回多个结果，使用第一个\n", compound_name))
        }

        props <- as.list(props_df[1, ])

        result <- list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = ifelse(!is.null(props$MolecularFormula) && !is.na(props$MolecularFormula) && nzchar(as.character(props$MolecularFormula)), as.character(props$MolecularFormula), NA),
          MolecularWeight = ifelse(!is.null(props$MolecularWeight) && !is.na(props$MolecularWeight), as.numeric(props$MolecularWeight), NA),
          InChIKey = ifelse(!is.null(props$InChIKey) && !is.na(props$InChIKey) && nzchar(as.character(props$InChIKey)), as.character(props$InChIKey), NA)
        )

        return(result)
      } else if (status_code(response) == 404) {
        cat(sprintf("❌ 化合物未找到: %s\n", compound_name))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
      } else {
        cat(sprintf("❌ HTTP错误 %d (尝试 %d/%d): %s\n", status_code(response), attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
      }
    }

    cat(sprintf("❌ 查询失败，已重试 %d 次: %s\n", max_retries, compound_name))
    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  add_compound_info_to_df <- function(df, compound_col, delay = 1) {
    # 确保所有需要的字段都存在
    for (field in allowed_fields) {
      if (!(field %in% colnames(df))) {
        df[[field]] <- NA
      }
    }

    # 确定需要查询的行
    if (force_query_all) {
      need_query <- seq_len(nrow(df))
      cat(sprintf("🔄 强制查询所有 %d 行\n", length(need_query)))
    } else {
      # 检查多个字段来决定是否需要查询
      need_query <- which(
        is.na(df$InChIKey) | df$InChIKey == "" |
        (("SMILES" %in% fields_to_update) & (is.na(df$SMILES) | df$SMILES == ""))
      )
      cat(sprintf("🔄 需要查询 %d 行（共 %d 行）\n", length(need_query), nrow(df)))
    }

    if (length(need_query) == 0) {
      cat("✅ 所有数据已完整，无需查询\n")
      return(df)
    }

    success_count <- 0
    for (i in need_query) {
      compound_name <- df[[compound_col]][i]
      cat(sprintf("🔍 查询 (%d/%d): %s\n", which(need_query == i), length(need_query), compound_name))

      result <- get_compound_info_from_pubchem(compound_name, delay = delay)

      # 更新指定的字段
      for (field in fields_to_update) {
        if (!is.na(result[[field]])) {
          df[[field]][i] <- result[[field]]
        }
      }

      # 检查查询结果 - 详细显示获取到的信息
      found_fields <- c()

      # 检查每个字段是否获取到数据
      if (!is.na(result$SMILES) && result$SMILES != "") {
        found_fields <- c(found_fields, "SMILES")
      }
      if (!is.na(result$MolecularFormula) && result$MolecularFormula != "") {
        found_fields <- c(found_fields, "分子式")
      }
      if (!is.na(result$MolecularWeight) && !is.na(as.numeric(result$MolecularWeight))) {
        found_fields <- c(found_fields, "分子量")
      }
      if (!is.na(result$InChIKey) && result$InChIKey != "") {
        found_fields <- c(found_fields, "InChIKey")
      }

      # 显示查询结果
      if (length(found_fields) > 0) {
        cat("✅ 查询成功 - 获得:", paste(found_fields, collapse=", "), "\n")
        success_count <- success_count + 1

        # 显示具体数据（可选，如果想看具体值的话）
        if (!is.na(result$SMILES) && result$SMILES != "") {
          cat("   SMILES:", substr(result$SMILES, 1, 50), if(nchar(result$SMILES) > 50) "..." else "", "\n")
        }
      } else {
        cat("❌ 查询失败或结果为空\n")
      }

      Sys.sleep(delay)
    }

    cat(sprintf("📊 查询完成：成功 %d/%d\n", success_count, length(need_query)))
    return(df)
  }

  # 读取Excel文件
  cat("📖 读取文件:", file_path, "\n")
  df <- read_excel(file_path)
  cat(sprintf("📊 文件包含 %d 行数据\n", nrow(df)))

  # 检查NAME列是否存在
  if (!"NAME" %in% colnames(df)) {
    stop("❌ 文件中未找到 'NAME' 列")
  }

  # 清洗NAME字段（根据你的需要修改）
  cat("🧹 清洗化合物名称...\n")
  df$NAME <- str_replace(df$NAME, "^Massbank:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "MassbankEU:SM\\d+\\s", "")
  df$NAME <- str_extract(df$NAME, "[^|]+")
  df$NAME <- trimws(df$NAME)  # 去除首尾空格

  # 显示一些样本名称
  sample_names <- head(df$NAME[!is.na(df$NAME) & df$NAME != ""], 3)
  if (length(sample_names) > 0) {
    cat("📝 样本化合物名称:", paste(sample_names, collapse = ", "), "...\n")
  }

  # 查询PubChem信息
  cat("🔍 开始查询 PubChem 数据库...\n")
  df_result <- add_compound_info_to_df(df, compound_col = "NAME", delay = delay)

  # 设置输出路径
  if (is.null(output_path)) {
    date_str <- format(Sys.Date(), "%Y%m%d")
    base_name <- tools::file_path_sans_ext(basename(file_path))
    output_path <- paste0(base_name, "_updated_", date_str, ".xlsx")
  }

  # 保存结果
  cat("💾 保存结果到:", output_path, "\n")
  write.xlsx(df_result, output_path)

  # 统计结果
  smiles_count <- sum(!is.na(df_result$SMILES) & df_result$SMILES != "", na.rm = TRUE)
  inchikey_count <- sum(!is.na(df_result$InChIKey) & df_result$InChIKey != "", na.rm = TRUE)

  cat("📦 查询完成！结果已保存到:", output_path, "\n")
  cat(sprintf("📈 统计结果: SMILES: %d/%d, InChIKey: %d/%d\n",
              smiles_count, nrow(df_result), inchikey_count, nrow(df_result)))
}

Add_pubchem_info_pro <- function(file_path, output_path = NULL,
                             delay = 0.5,
                             force_query_all = FALSE,
                             fields_to_update = c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")) {

  library(httr)
  library(jsonlite)
  library(readxl)
  library(openxlsx)
  library(stringr)

  allowed_fields <- c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")
  fields_to_update <- intersect(fields_to_update, allowed_fields)
  if (length(fields_to_update) == 0) stop("❌ 请至少选择一个有效字段进行查询")

  get_valid_smiles <- function(props) {
    if (is.list(props) || is.data.frame(props)) {
      smiles <- props$SMILES
      if (!is.null(smiles) && nzchar(smiles)) return(as.character(smiles))

      iso <- props$IsomericSMILES
      if (!is.null(iso) && nzchar(iso)) return(as.character(iso))

      cano <- props$CanonicalSMILES
      if (!is.null(cano) && nzchar(cano)) return(as.character(cano))

      conn <- props$ConnectivitySMILES
      if (!is.null(conn) && nzchar(conn)) return(as.character(conn))
    }
    return(NA)
  }

  get_compound_info_from_pubchem <- function(compound_name, max_retries = 3, delay = 1) {
    if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    name_encoded <- URLencode(compound_name, reserved = TRUE)
    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/", name_encoded,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        cat(sprintf("❌ 网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          cat(sprintf("❌ JSON解析错误: %s\n", compound_name))
          Sys.sleep(delay * attempt)
          next
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        props <- as.list(props_df[1, ])

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA
        ))
      } else if (status_code(response) == 404) {
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
      } else {
        cat(sprintf("❌ HTTP错误 %d (尝试 %d/%d): %s\n", status_code(response), attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  get_compound_info_by_inchikey <- function(inchikey, max_retries = 3, delay = 1) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/inchikey/", inchikey,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        cat(sprintf("❌ 网络错误 (InChIKey 尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          Sys.sleep(delay * attempt)
          next
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        props <- as.list(props_df[1, ])

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA
        ))
      } else if (status_code(response) == 404) {
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
      } else {
        cat(sprintf("❌ HTTP错误 %d (InChIKey 尝试 %d/%d): %s\n", status_code(response), attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  add_compound_info_to_df <- function(df, compound_col, delay = 1) {
    for (field in allowed_fields) {
      if (!(field %in% colnames(df))) df[[field]] <- NA
    }

    if (force_query_all) {
      need_query <- seq_len(nrow(df))
    } else {
      need_query <- which(
        is.na(df$InChIKey) | df$InChIKey == "" |
        (("SMILES" %in% fields_to_update) & (is.na(df$SMILES) | df$SMILES == ""))
      )
    }

    if (length(need_query) == 0) {
      cat("✅ 所有数据已完整，无需查询\n")
      return(df)
    }

    for (i in need_query) {
      compound_name <- df[[compound_col]][i]
      cat(sprintf("🔍 查询 (%d/%d): %s\n", which(need_query == i), length(need_query), compound_name))

      result <- get_compound_info_from_pubchem(compound_name, delay = delay)

      # 如果 NAME 查询失败，并且存在 InChIKey，尝试用 InChIKey 查
      if (all(is.na(result[fields_to_update]) | result[fields_to_update] == "") &&
          !is.na(df$InChIKey[i]) && df$InChIKey[i] != "") {
        cat("🔁 通过名称查询失败，尝试使用 InChIKey 再查...\n")
        result <- get_compound_info_by_inchikey(df$InChIKey[i], delay = delay)
      }

      for (field in fields_to_update) {
        if (!is.na(result[[field]])) {
          df[[field]][i] <- result[[field]]
        }
      }

      found <- fields_to_update[!is.na(result[fields_to_update])]
      if (length(found) > 0) {
        cat("✅ 查询成功 - 获得:", paste(found, collapse = ", "), "\n")
      } else {
        cat("❌ 查询失败或结果为空\n")
      }

      Sys.sleep(delay)
    }

    return(df)
  }

  `%||%` <- function(a, b) if (!is.null(a)) a else b

  # --- 主逻辑 ---
  cat("📖 读取文件:", file_path, "\n")
  df <- read_excel(file_path)
  if (!"NAME" %in% colnames(df)) stop("❌ 文件中未找到 'NAME' 列")
  cat(sprintf("📊 文件包含 %d 行数据\n", nrow(df)))

  cat("🧹 清洗化合物名称...\n")
  df$NAME <- str_replace(df$NAME, "^Massbank:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^ReSpect:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCGC\\d+-\\d+!", "")
  df$NAME <- str_replace(df$NAME, "^MoNA:\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "MassbankEU:SM\\d+\\s", "")
  df$NAME <- str_extract(df$NAME, "[^|]+")
  df$NAME <- str_extract(df$NAME, "^[^;]+")
  df$NAME <- trimws(df$NAME)
  df <- df[!str_detect(df$NAME, "^Unknown"), ]



  sample_names <- head(df$NAME[!is.na(df$NAME) & df$NAME != ""], 3)
  if (length(sample_names) > 0) cat("📝 样本化合物名称:", paste(sample_names, collapse = ", "), "...\n")

  cat("🔍 开始查询 PubChem 数据库...\n")
  df_result <- add_compound_info_to_df(df, compound_col = "NAME", delay = delay)

  if (is.null(output_path)) {
    date_str <- format(Sys.Date(), "%Y%m%d")
    base_name <- tools::file_path_sans_ext(basename(file_path))
    output_path <- paste0(base_name, "_updated_", date_str, ".xlsx")
  }

  cat("💾 保存结果到:", output_path, "\n")
  write.xlsx(df_result, output_path)

  smiles_count <- sum(!is.na(df_result$SMILES) & df_result$SMILES != "", na.rm = TRUE)
  inchikey_count <- sum(!is.na(df_result$InChIKey) & df_result$InChIKey != "", na.rm = TRUE)

  cat("📦 查询完成！结果已保存到:", output_path, "\n")
  cat(sprintf("📈 统计结果: SMILES: %d/%d, InChIKey: %d/%d\n",
              smiles_count, nrow(df_result), inchikey_count, nrow(df_result)))
}

#####################################################
#                      mz匹配                        #
#             *使用时更改参数设置部分路径*               #
#####################################################
# mz值匹配函数
mz_match <- function(medicine_file, sim_file, output, diff) {

  if(!dir.exists(output)){
    dir.create(output)
  }
  # 从File2列中提取mz值的内部函数
  extract_mz <- function(file2_str) {
    pattern <- "nM([0-9]+\\.?[0-9]*)"
    matches <- regmatches(file2_str, regexpr(pattern, file2_str))
    if (length(matches) > 0 && matches != "") {
      mz_pattern <- "([0-9]+\\.?[0-9]*)"
      mz_matches <- regmatches(matches, regexpr(mz_pattern, matches))
      if (length(mz_matches) > 0) {
        return(as.numeric(mz_matches))
      }
    }
    return(NA)
  }

  # 读取输入文件
  medicine_df <- read.csv(medicine_file, stringsAsFactors = FALSE)
  sim_df <- read.csv(sim_file, stringsAsFactors = FALSE)

  # 从SIM.csv的File2列中提取mz值
  sim_df$extracted_mz <- sapply(sim_df$File2, extract_mz)
  sim_df <- sim_df[!is.na(sim_df$extracted_mz), ]

  # 存储匹配结果
  matched_records <- data.frame()

  # 匹配
  for (i in 1:nrow(medicine_df)) {
    med_mz <- medicine_df[i, "mz"]
    mz_diff <- abs(sim_df$extracted_mz - med_mz)
    matching_indices <- which(mz_diff <= diff)

    if (length(matching_indices) > 0) {
      for (j in matching_indices) {
        matched_record <- data.frame(
          Medicine_ID = medicine_df[i, "X"],
          Medicine_mz = med_mz,
          Medicine_RT = medicine_df[i, "rt"],
          SIM_mz = sim_df[j, "extracted_mz"],
          SIM_Name = sim_df[j, "NAME"],
          SIM_InChIKey = sim_df[j, "INCHIKEY"],
          SIM_PC_Group = sim_df[j, "PC_Group"],
          mz_Difference = abs(sim_df[j, "extracted_mz"] - med_mz),
          stringsAsFactors = FALSE
        )
        matched_records <- rbind(matched_records, matched_record)
      }
    }
  }

  # 按差值排序
  if (nrow(matched_records) > 0) {
    matched_records <- matched_records[order(matched_records$mz_Difference), ]
  }

  # 保存结果
  write.csv(matched_records, file.path(paste0(output,"/mz_match.csv")), row.names = FALSE)

  # 返回结果
  return(matched_records)
}


#####################################################
#       合并同一张谱中来源于同一个母离子的二级碎片           #
#             *使用时更改参数设置部分路径*               #
#####################################################
merge_fragments_by_precursor <- function (input_path , output_path, rtTol=5, mzTol=0.005){
library(metid)
library(tidyverse)
library(gtools)
library(dplyr)
library(openxlsx)

  if(!dir.exists(output_path)) {
    dir.create(output_path)
    dir.create(file.path(paste0(output_path, "/01_excel_he")))
  }else if(!dir.exists(file.path(paste0(output_path, "/01_excel_he")))){
    dir.create(file.path(paste0(output_path, "/01_excel_he")))
  }
setwd(file.path(paste0(output_path,"/01_excel_he")))
save_path <- file.path(paste0(output_path,"/01_excel_he"))
path <- file.path(input_path)
file.pos <- mixedsort(dir(file.path(path), pattern = "\\.mzXML$|\\.MzML", full.names = TRUE))

matrix_ms1_all_m <- NULL
for(m in 1:length(file.pos)){
  file.pos_m <- file.pos[m]
  ms2.data.pos <- read_mzxml(file = file.pos_m, threads = threads)
  ms1.info.pos <- lapply(ms2.data.pos, function(x) {
    x[[1]]
  })
  ms1.info.pos <- do.call(rbind, ms1.info.pos) %>% as.data.frame()
  ms2.info.pos <- lapply(ms2.data.pos, function(x) {
    x[[2]]
  })
  order_data <- ms1.info.pos
  order_data$Designate_PC <- rep(NA,dim(order_data)[1])
  order_data[1,dim(ms1.info.pos)[2]+1] = paste("PC",1,sep = "")
  PC_record <- as.data.frame(order_data[1,c("mz","rt","Designate_PC")])

  if (length(order_data$Designate_PC)>0){
    if(length(order_data$Designate_PC) > 1){
      for (i in 2:length(order_data$Designate_PC)){
        loc <- which(order_data[i,"rt"] >= PC_record$rt - rtTol & order_data[i,"rt"] <= PC_record$rt + rtTol & order_data[i,"mz"] >= PC_record$mz - mzTol & order_data[i,"mz"] <= PC_record$mz + mzTol)
        if(length(loc) >0 ){
          if(length(loc) >1 ){
            mz_rt_diff <- list()
            for (j in 1:length(loc)){
              mz_rt_diff[[j]] <- sqrt((order_data[i,"rt"] - PC_record$rt[loc[j]])^2+(order_data[i,"mz"] - PC_record$mz[loc[j]])^2)
            }
            mz_rt_diff_min <- which.min(mz_rt_diff)
            order_data$Designate_PC[i] <- PC_record$Designate_PC[loc][mz_rt_diff_min]
            Update_PC <- PC_record$Designate_PC[loc][mz_rt_diff_min]
            PC_loc <- which(Update_PC==order_data$Designate_PC)
            H_rt <- order_data[PC_loc,"rt"]
            H_mz <- order_data[PC_loc,"mz"]
            PC_record$rt[loc][mz_rt_diff_min] <- mean(H_rt)
            PC_record$mz[loc][mz_rt_diff_min] <- mean(H_mz)
          }else{
            order_data$Designate_PC[i] <- PC_record$Designate_PC[loc]
            Update_PC <- PC_record$Designate_PC[loc]
            PC_loc <- which(order_data$Designate_PC == Update_PC)
            H_rt <- order_data[PC_loc,"rt"]
            H_mz <- order_data[PC_loc,"mz"]
            PC_record$rt[loc] <- mean(H_rt)
            PC_record$mz[loc] <- mean(H_mz)
          }
        }else{
          n <- length(PC_record$Designate_PC)+1
          order_data$Designate_PC[i] = paste("PC",n,sep = "")
          PC_record <- rbind(PC_record,order_data[i,c("mz","rt","Designate_PC")])
        }
      }
      matrix_ms2_all <- NULL
      matrix_ms1_all <- NULL


      for(nm in 1:dim(PC_record)[1]){
        ind_n <- which(order_data$Designate_PC %in% PC_record$Designate_PC[nm])
        list_ord <- ms2.info.pos[ind_n]
        list_ord <- list_ord[which(sapply(list_ord, nrow) != 0)]

        #######################################################################
        # 修改的关键部分：处理list_ord为空的情况
        if (length(list_ord) == 0) {
          # 当没有MS2数据时，保存母离子信息并创建空的MS2数据框
          matrix_ms1 <- PC_record[nm, ]
          matrix_ms2_all[[nm]] <- data.frame(mz = numeric(),
                                             intensity = numeric(),
                                             stringsAsFactors = FALSE)
          matrix_ms1_all <- rbind(matrix_ms1_all, matrix_ms1)

          # 生成文件名（保持与有数据时相同的格式）
          mzdec <- 4; rtdec <- 3
          rtfmt <- paste("%.", rtdec, "f", sep = "")
          mzfmt <- paste("%.", mzdec, "f", sep = "")
          var1 <- paste("nM", sprintf(mzfmt, matrix_ms1[,"mz"]),
                        "T", sprintf(rtfmt, matrix_ms1[,"rt"]), sep = "")

          # 创建工作簿并保存（即使没有数据也保持文件结构一致）
          if(!exists("wb")) wb <- createWorkbook()
          addWorksheet(wb, sheetName = var1)
          writeData(wb, sheet = var1, x = matrix_ms2_all[[nm]])

          next  # 现在这个next是在for循环内，可以正常工作
        }

        # 原有处理逻辑保持不变
        list_ord_all <- list()
        for (df in 1:length(list_ord)) {
          if (nrow(list_ord[[df]]) > 0) {
            # 确保列名一致
            colnames(list_ord[[df]]) <- c("mz", "intensity")
            list_ord_all[[df]] <- list_ord[[df]]
          }
        }
        #合并来自同一母离子的二级碎片
        if(length(list_ord_all) > 0){
          mat_ord <- do.call(rbind,list_ord_all)
          #同时写出该二级碎片的母离子信息
          matrix_ms1 <- PC_record[nm,]
          #同一个碎片离子标记为相同的编号，为按强度加和做准备
          order_data_sum <- mat_ord
          order_data_sum$Designate_PC <- rep(NA,dim(order_data_sum)[1])
          order_data_sum[1,dim(mat_ord)[2]+1] = paste("PC",1,sep = "")
          PC_record_sum <- as.data.frame(order_data_sum[1,c("mz","intensity","Designate_PC")])
          mzTol <- 0.005
          if(length(order_data_sum$Designate_PC) > 1){
            for (ii in 2:length(order_data_sum$Designate_PC)){
              loc <- which(order_data_sum[ii,"mz"] >= PC_record_sum$mz - mzTol & order_data_sum[ii,"mz"] <= PC_record_sum$mz + mzTol)
              if(length(loc) >0 ){
                if(length(loc) >1 ){
                  mz_rt_diff <- list()
                  #添加化学位移和sigma及gamma的误差
                  for (jj in 1:length(loc)){
                    mz_rt_diff[[jj]] <- sqrt((order_data_sum[ii,"mz"] - PC_record_sum$mz[loc[jj]])^2)
                  }
                  mz_rt_diff_min <- which.min(mz_rt_diff)
                  order_data_sum$Designate_PC[ii] <- PC_record_sum$Designate_PC[loc][mz_rt_diff_min]
                  Update_PC <- PC_record_sum$Designate_PC[loc][mz_rt_diff_min]
                  PC_loc <- which(Update_PC==order_data_sum$Designate_PC)
                  H_mz <- order_data_sum[PC_loc,"mz"]
                  PC_record_sum$mz[loc][mz_rt_diff_min] <- mean(H_mz)
                }else{
                  order_data_sum$Designate_PC[ii] <- PC_record_sum$Designate_PC[loc]
                  Update_PC <- PC_record_sum$Designate_PC[loc]
                  PC_loc <- which(order_data_sum$Designate_PC == Update_PC)
                  H_mz <- order_data_sum[PC_loc,"mz"]
                  PC_record_sum$mz[loc] <- mean(H_mz)
                }
                # If the chosen peak is located outside the tolerance of any existing PC
              }else{
                n_sum <- length(PC_record_sum$Designate_PC)+1
                order_data_sum$Designate_PC[ii] = paste("PC",n_sum,sep = "")
                PC_record_sum <- rbind(PC_record_sum,order_data_sum[ii,c("mz","intensity","Designate_PC")])
              }
            }
          }

          if(length(order_data_sum$Designate_PC) == 1){
            PC_record_sum <- PC_record_sum
            order_data_sum <- order_data_sum
          }
          #对相同的编号按强度加和
          sum1a <- order_data_sum %>%
            group_by(Designate_PC) %>%
            summarise_at(vars(2), sum)

          sum1 <- as.data.frame(sum1a)
          #合并二级碎片mz信息
          for(p in 1:dim(sum1)[1]){
            for(q in 1:dim(PC_record_sum)[1]){
              if (sum1$Designate_PC[p] %in% PC_record_sum$Designate_PC[q]){
                sum1[p,3:5] <- PC_record_sum[q,]
              }
            }
          }
          #写出同一离子的二级碎片加和结果
          matrix_ms2 <- sum1[,c(3,2)]
          matrix_ms2_all[[nm]] <- arrange(matrix_ms2,mz)
          matrix_ms1_all <- rbind(matrix_ms1_all,matrix_ms1)
          mzdec <- 4; rtdec <- 3;
          rtfmt <- paste("%.", rtdec, "f", sep = "")
          mzfmt <- paste("%.", mzdec, "f", sep = "")
          var1<- paste("nM", sprintf(mzfmt, matrix_ms1_all[,"mz"]), "T", sprintf(rtfmt, matrix_ms1_all[,"rt"]), sep = "")
          rownames(matrix_ms1_all) <- var1
          # 创建一个新的Excel工作簿
          wb <- createWorkbook()
          # 循环遍历列表中的矩阵，并将它们添加到工作簿中
          for (i in seq_along(matrix_ms2_all)) {
            # 添加一个新的工作表，名称为矩阵的名称
            addWorksheet(wb, sheetName = rownames(matrix_ms1_all)[i])
            # 将矩阵写入当前工作表
            writeData(wb, sheet = rownames(matrix_ms1_all)[i], x = matrix_ms2_all[[i]])
          }
          # 保存工作簿到文件
          saveWorkbook(wb, file = file.path(save_path, paste0(basename(file.pos_m), ".xlsx")), overwrite = TRUE)
        }
      }
    }
  }

  if(length(order_data$Designate_PC) == 1){
    # 创建一个新的Excel工作簿
    wb <- createWorkbook()
    # 添加一个新的工作表，名称为矩阵的名称
    addWorksheet(wb, sheetName = order_data[,1])
    # 将矩阵写入当前工作表
    writeData(wb, sheet = order_data[,1], x = ms2.info.pos[[1]])

    # 保存工作簿到文件
    saveWorkbook(wb, file = file.path(save_path, paste0(basename(file.pos_m), ".xlsx")), overwrite = TRUE)
  }

  matrix_ms1_all_m <- rbind(matrix_ms1_all_m,matrix_ms1_all)

}



############################合并结束

# 读取所有采集的excel
path <- file.path(paste0(output_path, "/01_excel_he"))
file.pos <- mixedsort(dir(file.path(path), pattern = "\\.xlsx$$", full.names = TRUE))
name_all <- NULL
for(l in 1:length(file.pos)){
  # 加载Excel文件
  wb <- file.pos[l]
  name_l <- getSheetNames(wb)
  name_l_file <- data.frame(name_l,wb)
  name_all <- rbind(name_all,name_l_file)
}
dim(name_all)
name_all[which(grepl("mz\\d+(\\.\\d+)?", name_all$name_l)),]$name_l <- gsub("mz", "nM", gsub("rt", "T", (name_all[which(grepl("mz\\d+(\\.\\d+)?", name_all$name_l)),])$name_l))
# 移除前缀'nM'，并以'T'为分隔符分割剩余的字符串
df_processed <- name_all %>%
  mutate(
    # 移除前缀'nM'
    ProcessedData = stringr::str_remove(name_l, "^nM"),
    # 分割处理后的数据
    Part1 = stringr::str_split_fixed(ProcessedData, "T", 2)[, 1],
    Part2 = stringr::str_split_fixed(ProcessedData, "T", 2)[, 2]
  )
# 查看结果
print(df_processed)
# 如果你不再需要原始的SampleData列或ProcessedData列，可以选择性地删除它们
df_final <- df_processed %>%
  select( -ProcessedData)
# 查看最终的数据框
print(df_final)
colnames(df_final) <- c("name_l","wb","mz","rt")
write.csv(df_final, file.path(paste0(output_path, "/allms2_acqired_1.csv")))


###2.1生成MyData_excel
library(readxl)
library(openxlsx)
library(parallel)

input_folder  <- file.path(paste0(output_path,"/01_excel_he"))
output_folder <- file.path(paste0(output_path ,"/02_MyData_excel"))

# 创建输出文件夹，如果不存在则创建
if(!dir.exists(output_folder)){
  dir.create(output_folder)
}

# 获取文件夹中的所有 Excel 文件
excel_files <- dir(input_folder, pattern = "\\.xlsx$", full.names = TRUE)

# 定义一个处理单个文件的函数
process_file <- function(file) {
  # 获取当前 Excel 文件中的所有工作表名称
  sheet_names <- excel_sheets(file)

  # 遍历每一个工作表
  for(sheet in sheet_names){
    sheet_data <- read_excel(file, sheet = sheet)
    output_file <- file.path(output_folder, paste0(sheet, ".csv"))
    write.csv(sheet_data, output_file, row.names = FALSE)
  }
}

num_cores <- detectCores() - 2
cl <- makeCluster(num_cores)
clusterEvalQ(cl, {
  library(readxl)
  library(openxlsx)
})

# 导出需要的变量和函数到集群
clusterExport(cl, varlist = c("process_file", "input_folder", "output_folder", "excel_files"),envir = environment())
parLapply(cl, excel_files, process_file)

stopCluster(cl)
}


#####################################################
#         msp文件批量处理，生成单独的xlsx                #
#             *使用时更改参数设置部分路径*               #
#####################################################
msp_search_merge <- function (input_path, output_base_path){
# 需要的包
library(readr)
library(openxlsx)
library(stringr)

# 1. 获取指定文件夹中的所有.msp文件
msp_files <- list.files(path = input_path, pattern = "\\.msp$", full.names = TRUE)

if (length(msp_files) == 0) {
  cat(sprintf("在路径 %s 中没有找到.msp文件！\n", input_path))
  quit()
}

cat(sprintf("在 %s 中找到 %d 个.msp文件，开始批量处理...\n", input_path, length(msp_files)))
cat(sprintf("输出路径: %s\n", output_base_path))

# 批量处理每个.msp文件
for (file_idx in seq_along(msp_files)) {
  msp_file <- msp_files[file_idx]
  cat(sprintf("正在处理: %s\n", basename(msp_file)))

  # 读取msp文件
  lines <- readLines(msp_file, encoding = "UTF-8")

  # 2. 解析内容
  get_blocks <- function(lines) {
    blocks <- list()
    block <- c()
    for (line in lines) {
      if (str_trim(line) == "" && length(block) > 0) {
        blocks[[length(blocks) + 1]] <- block
        block <- c()
      } else {
        block <- c(block, line)
      }
    }
    if (length(block) > 0) blocks[[length(blocks) + 1]] <- block
    return(blocks)
  }

  blocks <- get_blocks(lines)

  # 新建输出文件夹
  output_dir <- file.path(output_base_path, tools::file_path_sans_ext(basename(msp_file)))
  if (!dir.exists(output_dir)) dir.create(output_dir, recursive = TRUE)

  # 创建POS和NEG子文件夹
  pos_dir <- file.path(output_dir, "POS")
  neg_dir <- file.path(output_dir, "NEG")
  if (!dir.exists(pos_dir)) dir.create(pos_dir)
  if (!dir.exists(neg_dir)) dir.create(neg_dir)

  # 3. 处理每个block
  for (i in seq_along(blocks)) {
    block <- blocks[[i]]
    # 找到NAME和Num Peaks的位置
    name_idx <- grep("^NAME", block, ignore.case = TRUE)
    num_peaks_idx <- grep("^Num Peaks", block, ignore.case = TRUE)
    if (length(name_idx) == 0 || length(num_peaks_idx) == 0) next

    # 提取元信息（从NAME到Num Peaks，包含Num Peaks行）
    meta_info <- block[name_idx:num_peaks_idx]

    # 峰数据从Num Peaks下一行开始
    peaks_data_start <- num_peaks_idx + 1

    # 提取峰数据（从Num Peaks下一行到下两个空行之间）
    peaks_raw <- c()
    empty_count <- 0
    for (j in peaks_data_start:length(block)) {
      if (str_trim(block[j]) == "") {
        empty_count <- empty_count + 1
        if (empty_count == 2) break
      } else {
        peaks_raw <- c(peaks_raw, block[j])
        empty_count <- 0
      }
    }

    # 处理峰数据：按空格分割为mz和intens两列
    if (length(peaks_raw) > 0) {
      # 分割每行数据
      peaks_split <- strsplit(peaks_raw, "\\s+")
      # 提取mz和intens
      mz_values <- sapply(peaks_split, function(x) if(length(x) >= 1) x[1] else "")
      intens_values <- sapply(peaks_split, function(x) if(length(x) >= 2) x[2] else "")
      # 创建数据框
      peaks_df <- data.frame(
        mz = mz_values,
        intensity = intens_values,
        stringsAsFactors = FALSE
      )
    } else {
      # 如果没有峰数据，创建空的数据框
      peaks_df <- data.frame(
        mz = character(0),
        intensity = character(0),
        stringsAsFactors = FALSE
      )
    }

    # 4. 生成文件名
    msp_filename <- basename(msp_file)
    msp_prefix <- str_extract(msp_filename, "^[0-9]+")
    if (is.na(msp_prefix)) {
      msp_prefix <- tools::file_path_sans_ext(msp_filename)
    }
    name_num <- i
    # 提取PRECURSORMZ:后面的数字
    precursor_mz_line <- grep("^PRECURSORMZ:", block, ignore.case = TRUE)
    if (length(precursor_mz_line) > 0) {
      precursor_mz_value <- str_extract(block[precursor_mz_line[1]], "[0-9]+\\.[0-9]+")
      if (is.na(precursor_mz_value)) {
        precursor_mz_value <- "0"
      }
    } else {
      precursor_mz_value <- "0"
    }

    # 提取IONMODE:后面的值
    ionmode_line <- grep("^IONMODE:", block, ignore.case = TRUE)
    if (length(ionmode_line) > 0) {
      ionmode_value <- str_trim(sub("^IONMODE:", "", block[ionmode_line[1]]))
      if (tolower(ionmode_value) == "positive") {
        target_dir <- pos_dir
      } else if (tolower(ionmode_value) == "negative") {
        target_dir <- neg_dir
      } else {
        target_dir <- output_dir  # 默认放在主文件夹
      }
    } else {
      target_dir <- output_dir  # 默认放在主文件夹
    }

    out_file <- file.path(target_dir, sprintf("%s_%d_%s.xlsx", msp_prefix, name_num, precursor_mz_value))

    # 5. 写入Excel
    wb <- createWorkbook()
    addWorksheet(wb, "sheet1")
    addWorksheet(wb, "sheet2")

    # Sheet1: 峰数据，包含mz和intens两列
    writeData(wb, "sheet1", peaks_df, colNames = TRUE)

    # Sheet2: 完整的元信息（从NAME到Num Peaks）
    writeData(wb, "sheet2", data.frame(meta_info), colNames = FALSE)

    saveWorkbook(wb, out_file, overwrite = TRUE)
  }

  cat(sprintf("完成处理: %s\n", basename(msp_file)))
}

cat("所有.msp文件处理完成！\n")
}

#########################################################
# msp文件批量第二步处理，生成含相对强度和中兴丢失的表格            #
#             *使用时更改参数设置部分路径*                    #
#########################################################


# 加载必要的包
library(readxl)
library(dplyr)
library(writexl)
library(parallel)
library(foreach)
library(doParallel)

# 处理单个Excel文件的函数
process_excel_file <- function(file_path, output_dir) {
  
  # 获取文件名（不含路径和扩展名）
  file_name <- tools::file_path_sans_ext(basename(file_path))
  
  # 从文件名中提取母离子质量（第二个下划线后的数值）
  parts <- strsplit(file_name, "_")[[1]]
  if (length(parts) < 3) {
    stop(paste("文件名格式不正确:", file_name, "应包含至少两个下划线"))
  }
  
  # 提取母离子质量
  parent_ion_str <- parts[3]
  parent_ion <- as.numeric(parent_ion_str)
  
  if (is.na(parent_ion)) {
    stop(paste("无法从文件名中提取母离子质量:", parent_ion_str))
  }
  
  # 获取所有sheet名称
  all_sheets <- excel_sheets(file_path)
  
  # 读取第一个sheet进行处理
  data <- read_excel(file_path, sheet = 1)
  
  # 检查必要的列是否存在
  if (!"mz" %in% colnames(data)) {
    stop("Excel文件中缺少'mz'列")
  }
  if (!"intensity" %in% colnames(data)) {
    stop("Excel文件中缺少'intensity'列")
  }
  
  # 转换数据类型（处理可能的字符型数据）
  data$mz <- as.numeric(data$mz)
  data$intensity <- as.numeric(data$intensity)
  
  # 检查转换后是否有NA值
  if (any(is.na(data$mz))) {
    warning("mz列中有无法转换的值")
  }
  if (any(is.na(data$intensity))) {
    warning("intensity列中有无法转换的值")
  }
  
  original_rows <- nrow(data)
  
  # 1. 按强度列降序排列
  data <- data %>% arrange(desc(intensity))
  
  # 2. 删除大于母离子+0.005的行（允许0.005误差范围）
  threshold <- parent_ion + 0.005
  data <- data %>% filter(mz <= threshold)
  
  filtered_rows <- nrow(data)
  
  # 3. 保留前60行（如果少于60行则全部保留）
  if (nrow(data) > 60) {
    data <- data %>% slice_head(n = 60)
    final_rows <- 60
  } else {
    final_rows <- nrow(data)
  }
  
  # 4. 计算中性丢失（母离子 - mz）
  data <- data %>% mutate(Loss_Neutral = abs(parent_ion - mz))
  
  # 5. 计算相对强度
  total_intensity <- sum(data$intensity, na.rm = TRUE)
  data <- data %>% mutate(relative_intensity = intensity / total_intensity)
  
  # 6. 计算熵值 E = -Sum(相对强度 * log(相对强度))
  valid_rel_int <- data$relative_intensity[data$relative_intensity > 0]
  if (length(valid_rel_int) > 0) {
    E_value <- -sum(valid_rel_int * log(valid_rel_int))
  } else {
    E_value <- 0
  }
  
  # 7. 输出到新文件
  E_rounded <- round(E_value, 4)
  output_file_name <- paste0(file_name, "_", E_rounded, ".xlsx")
  output_file_path <- file.path(output_dir, output_file_name)
  
  # 准备输出数据：处理后的第一个sheet + 其他原始sheet
  output_data <- list()
  output_data[[all_sheets[1]]] <- data
  
  # 如果有其他sheet，原样复制
  if (length(all_sheets) > 1) {
    for (i in 2:length(all_sheets)) {
      sheet_name <- all_sheets[i]
      other_sheet_data <- read_excel(file_path, sheet = i)
      output_data[[sheet_name]] <- other_sheet_data
    }
  }
  
  # 保存处理后的数据（包含所有sheet）
  # 使用format_headers=FALSE来避免自动格式化
  write_xlsx(output_data, output_file_path, format_headers = FALSE)
  
  return(list(
    file_name = file_name,
    parent_ion = parent_ion,
    original_rows = original_rows,
    filtered_rows = filtered_rows,
    final_rows = final_rows,
    entropy = E_value,
    output_path = output_file_path,
    processing_time = Sys.time()
  ))
}

# 主函数：并行处理Excel文件
#' 并行处理Excel质谱数据文件
#'
#' @param input_dir 输入文件夹路径
#' @param output_dir 输出文件夹路径
#' @param num_cores 使用的CPU核心数，默认为NULL（自动检测）
#' @param verbose 是否显示详细信息，默认为TRUE
#' @return 处理结果列表
process_mspexcel_parallel <- function(input_dir = "input",
                                   output_dir = "output",
                                   num_cores = NULL,
                                   verbose = TRUE) {
  
  if (verbose) {
    cat("=== Excel文件并行处理程序 ===\n")
    cat("开始时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
    cat("输入文件夹:", input_dir, "\n")
    cat("输出文件夹:", output_dir, "\n\n")
  }
  
  # 创建输出文件夹
  if (!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
    if (verbose) cat("✅ 创建输出文件夹:", output_dir, "\n")
  }
  
  # 扫描输入文件
  excel_files <- list.files(input_dir, pattern = "\\.xlsx?$", full.names = TRUE)
  
  if (length(excel_files) == 0) {
    if (verbose) cat("❌ 在", input_dir, "文件夹中没有找到Excel文件\n")
    return(list(
      success = FALSE,
      message = "没有找到Excel文件",
      files_processed = 0,
      results = list()
    ))
  }
  
  if (verbose) {
    cat("📁 找到", length(excel_files), "个Excel文件:\n")
    for (i in seq_along(excel_files)) {
      cat("  ", i, ".", basename(excel_files[i]), "\n")
    }
    cat("\n")
  }
  
  # 设置并行处理
  total_cores <- detectCores()
  if (is.null(num_cores)) {
    use_cores <- max(1, min(total_cores - 1, length(excel_files)))
  } else {
    use_cores <- max(1, min(num_cores, total_cores, length(excel_files)))
  }
  
  if (verbose) {
    cat("💻 检测到", total_cores, "个CPU核心\n")
    cat("🚀 将使用", use_cores, "个核心进行并行处理\n\n")
  }
  
  # 开始并行处理
  if (verbose) cat("⏱️  开始并行处理...\n")
  start_time <- Sys.time()
  
  # 设置并行后端
  cl <- makeCluster(use_cores)
  registerDoParallel(cl)
  
  # 并行处理所有文件
  results <- foreach(file_path = excel_files,
                     .packages = c("readxl", "dplyr", "writexl"),
                     .errorhandling = "pass",
                     .export = c("process_excel_file")) %dopar% {
                       tryCatch({
                         process_excel_file(file_path, output_dir)
                       }, error = function(e) {
                         list(error = TRUE, file = file_path, message = e$message)
                       })
                     }
  
  # 停止并行后端
  stopCluster(cl)
  
  end_time <- Sys.time()
  processing_time <- end_time - start_time
  
  if (verbose) {
    cat("✅ 并行处理完成！\n")
    cat("⏱️  总耗时:", round(processing_time, 2), attr(processing_time, "units"), "\n\n")
  }
  
  # 统计结果
  success_count <- 0
  error_count <- 0
  successful_results <- list()
  error_results <- list()
  
  for (i in seq_along(results)) {
    result <- results[[i]]
    if (!is.null(result$error) && result$error) {
      error_count <- error_count + 1
      error_results[[length(error_results) + 1]] <- result
      if (verbose) {
        cat("❌ 文件", i, ":", basename(result$file), "\n")
        cat("   错误:", result$message, "\n\n")
      }
    } else {
      success_count <- success_count + 1
      successful_results[[length(successful_results) + 1]] <- result
      if (verbose) {
        cat("✅ 文件", i, ":", result$file_name, "\n")
        cat("   母离子质量:", result$parent_ion, "\n")
        cat("   数据行数: 原始", result$original_rows, "→ 过滤", result$filtered_rows, "→ 最终", result$final_rows, "\n")
        cat("   熵值 E:", round(result$entropy, 6), "\n")
        cat("   输出文件:", basename(result$output_path), "\n\n")
      }
    }
  }
  
  # 输出统计信息
  if (verbose) {
    cat("=== 📈 处理统计 ===\n")
    cat("总文件数:", length(excel_files), "\n")
    cat("成功处理:", success_count, "\n")
    cat("处理失败:", error_count, "\n")
    cat("成功率:", round(success_count/length(excel_files)*100, 1), "%\n")
    cat("总处理时间:", round(processing_time, 2), attr(processing_time, "units"), "\n")
    if (success_count > 0) {
      avg_time <- as.numeric(processing_time) / success_count
      cat("平均每文件:", round(avg_time, 2), attr(processing_time, "units"), "\n")
    }
    cat("\n🎉 程序执行完成！\n")
    cat("结束时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
  }
  
  # 返回处理结果
  return(list(
    success = success_count > 0,
    total_files = length(excel_files),
    success_count = success_count,
    error_count = error_count,
    success_rate = round(success_count/length(excel_files)*100, 1),
    processing_time = processing_time,
    successful_results = successful_results,
    error_results = error_results,
    input_dir = input_dir,
    output_dir = output_dir,
    cores_used = use_cores
  ))
}



#########################################################
#        合并文件夹中的文件夹下的所有文件到输出文件夹             #
#             *使用时更改参数设置部分路径*                    #
#########################################################
#' 处理单个文件的复制
#' @param file 源文件路径
#' @param output_path 目标文件夹路径
#' @return 包含处理结果的列表
process_file_1 <- function(file, output_path) {
  tryCatch({
    filename <- basename(file)
    dest_file <- file.path(output_path, filename)

    # 如果文件名冲突，添加序号
    counter <- 1
    while (file.exists(dest_file)) {
      name_parts <- tools::file_path_sans_ext(filename)
      extension <- tools::file_ext(filename)

      if (extension == "") {
        new_name <- paste0(name_parts, "_", counter)
      } else {
        new_name <- paste0(name_parts, "_", counter, ".", extension)
      }

      dest_file <- file.path(output_path, new_name)
      counter <- counter + 1
    }

    # 复制文件
    success <- file.copy(file, dest_file, overwrite = FALSE)

    if (success) {
      return(list(
        success = TRUE,
        source = file,
        dest = dest_file,
        size = file.info(file)$size
      ))
    } else {
      return(list(
        success = FALSE,
        source = file,
        dest = dest_file,
        error = "文件复制失败"
      ))
    }
  }, error = function(e) {
    return(list(
      success = FALSE,
      source = file,
      dest = "",
      error = paste("错误:", e$message)
    ))
  })
}

#' 并行合并文件夹函数
#' @param input_path 输入文件夹路径
#' @param output_path 输出文件夹路径
#' @param num_cores 使用的CPU核心数，默认为NULL（自动检测）
#' @param verbose 是否显示详细信息，默认为TRUE
#' @return 包含处理结果的数据框
merge_folders <- function(input_path, output_path, num_cores = NULL, verbose = TRUE) {
  library(parallel)
  library(foreach)
  library(doParallel)

  # 检查输入路径是否存在
  if (!dir.exists(input_path)) {
    stop("输入路径不存在: ", input_path)
  }

  # 创建输出文件夹
  if (!dir.exists(output_path)) {
    dir.create(output_path, recursive = TRUE)
  }

  # 设置核心数
  if (is.null(num_cores)) {
    num_cores <- max(1, detectCores() - 1)  # 保留一个核心给系统
  }

  if (verbose) {
    cat("使用", num_cores, "个CPU核心进行并行处理\n")
  }

  # 获取所有子文件夹
  subfolders <- list.dirs(input_path, recursive = TRUE, full.names = TRUE)
  subfolders <- subfolders[subfolders != input_path]  # 排除根目录

  # 收集所有文件
  all_files <- c()
  for (folder in subfolders) {
    files <- list.files(folder, full.names = TRUE, recursive = FALSE)
    files <- files[!dir.exists(files)]  # 只要文件，不要文件夹
    all_files <- c(all_files, files)
  }

  if (length(all_files) == 0) {
    if (verbose) cat("没有找到任何文件\n")
    return(data.frame())
  }

  if (verbose) {
    cat("找到", length(all_files), "个文件需要处理\n")
  }

  # 设置并行后端
  cl <- makeCluster(num_cores)
  registerDoParallel(cl)

  # 导出必要的函数和变量到工作节点
  clusterExport(cl, c("process_file_1", "output_path", "all_files"), envir = environment())
  clusterEvalQ(cl, library(tools))

  start_time <- Sys.time()

  # 并行处理文件
  results <- foreach(i = 1:length(all_files), .combine = rbind, .packages = c("tools")) %dopar% {
    file <- all_files[i]
    result <- process_file_1(file, output_path)
    data.frame(
      success = result$success,
      source = result$source,
      dest = result$dest,
      error = ifelse(result$success, "", result$error %||% ""),
      size = ifelse(result$success, result$size %||% 0, 0),
      stringsAsFactors = FALSE
    )
  }

  # 停止并行后端
  stopCluster(cl)

  end_time <- Sys.time()
  processing_time <- end_time - start_time

  # 统计结果
  successful_files <- sum(results$success)
  failed_files <- sum(!results$success)
  total_size <- sum(results$size[results$success], na.rm = TRUE)

  if (verbose) {
    cat("\n=== 处理完成 ===\n")
    cat("总文件数:", nrow(results), "\n")
    cat("成功复制:", successful_files, "\n")
    cat("复制失败:", failed_files, "\n")
    cat("总大小:", round(total_size / 1024 / 1024, 2), "MB\n")
    cat("处理时间:", round(processing_time, 2), attr(processing_time, "units"), "\n")

    # 显示失败的文件
    if (failed_files > 0) {
      cat("\n失败的文件:\n")
      failed_results <- results[!results$success, ]
      for (i in 1:nrow(failed_results)) {
        cat("  ", failed_results$source[i], "->", failed_results$dest[i], "(", failed_results$error[i], ")\n")
      }
    }
  }

  return(results)
}

#####################################################
#                    删除正则匹配的文件                #
#             *使用时更改参数设置部分路径*               #
#####################################################
delete_match_files <- function(directory = NULL, pattern = NULL) {
  # 如果没有提供目录，询问用户
  if (is.null(directory)) {
    directory <- readline(prompt = "请输入目标目录路径: ")
  }

  # 检查目录是否存在
  if (!dir.exists(directory)) {
    stop("错误: 指定的目录不存在: ", directory)
  }

  # 如果没有提供正则表达式，询问用户
  if (is.null(pattern)) {
    cat("请输入正则表达式模式来匹配要删除的文件:\n")
    cat("示例:\n")
    cat("  - .*_0\\.xlsx$     (匹配以'_0.xlsx'结尾的文件)\n")
    cat("  - ^temp.*\\.txt$  (匹配以'temp'开头、'.txt'结尾的文件)\n")
    cat("  - .*\\.log$       (匹配所有.log文件)\n")
    pattern <- readline(prompt = "正则表达式: ")
  }

  # 验证正则表达式
  tryCatch({
    grep(pattern, "test", perl = TRUE)
  }, error = function(e) {
    stop("错误: 无效的正则表达式: ", pattern)
  })

  # 设置工作目录
  original_wd <- getwd()
  on.exit(setwd(original_wd))  # 确保函数结束时恢复原目录
  setwd(directory)

  # 获取匹配的文件
  files_to_delete <- list.files(pattern = pattern, full.names = TRUE)

  # 检查是否有匹配的文件
  if (length(files_to_delete) == 0) {
    message("没有找到匹配模式 '", pattern, "' 的文件")
    return(invisible(NULL))
  }

  # 显示将要删除的文件
  message("在目录 '", directory, "' 中找到 ", length(files_to_delete), " 个匹配的文件:")
  cat("\n")
  for (i in seq_along(files_to_delete)) {
    cat(sprintf("%d. %s\n", i, basename(files_to_delete[i])))
  }
  cat("\n")

  # 确认是否继续删除
  user_input <- readline(prompt = "确认删除这些文件吗？(y/n): ")

  if (tolower(user_input) %in% c("y", "yes", "是")) {
    # 删除文件
    success_count <- 0
    failed_files <- character(0)

    for (file in files_to_delete) {
      if (file.remove(file)) {
        success_count <- success_count + 1
      } else {
        failed_files <- c(failed_files, file)
      }
    }

    # 报告结果
    if (success_count > 0) {
      message(sprintf("已成功删除 %d 个文件", success_count))
    }

    if (length(failed_files) > 0) {
      warning("以下文件删除失败:")
      for (file in failed_files) {
        cat("  - ", basename(file), "\n")
      }
    }
  } else {
    message("操作已取消")
  }
}


#####################################################
#      将msp生成的xlsx进行分类                      #
#       根据NAME是否相同分成两类                   #
#       根据相同的NAME中的MZ分为两类               #
#####################################################
# 加载必要的库
library(readxl)
library(dplyr)
library(stringr)
library(parallel)
library(foreach)
library(doParallel)

classify_excel_files <- function(source_dir = NULL, output_base_dir = NULL, n_cores = NULL, mz_tolerance = 0.005, convert_to_txt = TRUE) {
  # 检查源目录是否存在
  if (!dir.exists(source_dir)) {
    stop("错误: 指定的源目录不存在: ", source_dir)
  }
  
  # 创建输出目录结构
  different_dir <- file.path(output_base_dir, "Different")
  sim_dir <- file.path(output_base_dir, "SIM")
  sim_mz_dir <- file.path(sim_dir, "SIM_MZ")
  different_mz_dir <- file.path(sim_dir, "Different_MZ")
  
  # 创建目录
  dir.create(different_dir, recursive = TRUE, showWarnings = FALSE)
  dir.create(sim_dir, recursive = TRUE, showWarnings = FALSE)
  dir.create(sim_mz_dir, recursive = TRUE, showWarnings = FALSE)
  dir.create(different_mz_dir, recursive = TRUE, showWarnings = FALSE)
  
  # 获取所有Excel文件
  excel_files <- list.files(source_dir, pattern = "\\.xlsx?$", full.names = TRUE, ignore.case = TRUE)
  
  if (length(excel_files) == 0) {
    stop("在指定目录中没有找到Excel文件")
  }
  
  cat("找到", length(excel_files), "个Excel文件\n")
  
  if (convert_to_txt) {
    cat("📝 将转换为TXT格式（只包含Sheet1数据，不包含Sheet2）\n")
  } else {
    cat("📁 将保持Excel格式\n")
  }
  
  # 设置并行处理
  if (is.null(n_cores)) {
    n_cores <- min(detectCores() - 1, length(excel_files))  # 保留一个核心给系统
    n_cores <- max(1, n_cores)  # 至少使用1个核心
  }
  
  cat("使用", n_cores, "个CPU核心进行并行处理\n")
  
  # 注册并行后端
  cl <- makeCluster(n_cores)
  registerDoParallel(cl)
  
  # 确保在函数结束时关闭集群
  on.exit({
    stopCluster(cl)
    registerDoSEQ()  # 恢复到顺序处理
  })
  
  # 并行提取每个文件的信息
  cat("正在并行读取Excel文件信息...\n")
  
  # 使用foreach进行并行处理
  file_info_list <- foreach(file_path = excel_files,
                            .packages = c("readxl", "stringr"),
                            .export = c("extract_mother_ion"),
                            .errorhandling = "pass") %dopar% {
                              
                              file_name <- basename(file_path)
                              
                              tryCatch({
                                # 读取第二个sheet的NAME列
                                sheets <- excel_sheets(file_path)
                                if (length(sheets) < 2) {
                                  return(list(error = paste("文件", file_name, "没有第二个sheet")))
                                }
                                
                                # 读取第二个sheet的第一行第一列来获取NAME信息
                                sheet2_data <- read_excel(file_path, sheet = 2, col_names = FALSE, n_max = 1)
                                
                                # 检查第一行第一列是否包含NAME信息
                                if (nrow(sheet2_data) == 0 || ncol(sheet2_data) == 0) {
                                  return(list(error = paste("文件", file_name, "的第二个sheet为空")))
                                }
                                
                                first_cell <- sheet2_data[1, 1]
                                if (is.na(first_cell) || !grepl("NAME:", as.character(first_cell), ignore.case = TRUE)) {
                                  return(list(error = paste("文件", file_name, "的第二个sheet第一行第一列不包含NAME信息")))
                                }
                                
                                # 提取NAME值（NAME:后面的内容）
                                name_text <- as.character(first_cell)
                                name_value <- sub(".*NAME:\\s*", "", name_text, ignore.case = TRUE)
                                name_value <- trimws(name_value)  # 去除前后空格
                                
                                if (name_value == "" || is.na(name_value)) {
                                  return(list(error = paste("文件", file_name, "的NAME值为空")))
                                }
                                
                                # 提取母离子（文件名中第二个和第三个下划线之间的数字）
                                mother_ion <- extract_mother_ion(file_name)
                                
                                # 返回文件信息
                                return(list(
                                  file_path = file_path,
                                  file_name = file_name,
                                  name_value = as.character(name_value),
                                  mother_ion = mother_ion,
                                  success = TRUE
                                ))
                                
                              }, error = function(e) {
                                return(list(error = paste("读取文件", file_name, "时出错:", e$message)))
                              })
                            }
  
  # 处理并行结果
  file_info <- data.frame(
    file_path = character(),
    file_name = character(),
    name_value = character(),
    mother_ion = character(),
    stringsAsFactors = FALSE
  )
  
  error_count <- 0
  for (result in file_info_list) {
    if (!is.null(result$error)) {
      warning(result$error)
      error_count <- error_count + 1
    } else if (!is.null(result$success)) {
      file_info <- rbind(file_info, data.frame(
        file_path = result$file_path,
        file_name = result$file_name,
        name_value = result$name_value,
        mother_ion = result$mother_ion,
        stringsAsFactors = FALSE
      ))
    }
  }
  
  cat("\n并行处理完成！\n")
  cat("成功读取", nrow(file_info), "个文件的信息\n")
  if (error_count > 0) {
    cat("跳过", error_count, "个有问题的文件\n")
  }
  
  # 开始分类
  cat("开始文件分类...\n")
  
  # 按NAME分组
  name_groups <- split(file_info, file_info$name_value)
  
  # 创建NAME到数字的映射
  name_mapping <- data.frame(
    number = integer(),
    name = character(),
    stringsAsFactors = FALSE
  )
  
  folder_counter <- 1
  
  for (name_group in names(name_groups)) {
    group_files <- name_groups[[name_group]]
    
    if (nrow(group_files) == 1) {
      # 只有一个文件，放入Different文件夹
      copy_file_to_dir(group_files$file_path[1], different_dir, convert_to_txt)
      cat("文件", group_files$file_name[1], "-> Different (唯一NAME)\n")
    } else {
      # 多个文件有相同的NAME，需要进一步按母离子分类
      
      # 在SIM_MZ中创建以数字命名的子文件夹
      numeric_folder_name <- as.character(folder_counter)
      name_dir <- file.path(sim_mz_dir, numeric_folder_name)
      dir.create(name_dir, recursive = TRUE, showWarnings = FALSE)
      
      # 记录映射关系
      name_mapping <- rbind(name_mapping, data.frame(
        number = folder_counter,
        name = name_group,
        stringsAsFactors = FALSE
      ))
      
      # 按母离子分组（考虑误差容忍度）
      mz_grouping_result <- group_mother_ions_by_tolerance(group_files, mz_tolerance)
      mz_groups <- mz_grouping_result$groups
      invalid_mz_files <- mz_grouping_result$invalid
      
      # 处理无效的母离子文件（放入Different_MZ）
      if (nrow(invalid_mz_files) > 0) {
        for (k in seq_len(nrow(invalid_mz_files))) {
          copy_file_to_dir(invalid_mz_files$file_path[k], different_mz_dir, convert_to_txt)
          cat("文件", invalid_mz_files$file_name[k], "-> Different_MZ (无效母离子)\n")
        }
      }
      
      # 处理有效的母离子分组
      for (i in seq_along(mz_groups)) {
        mz_files <- mz_groups[[i]]
        group_mz <- mz_files$group_mz[1]  # 使用组的代表性母离子值
        
        if (nrow(mz_files) == 1) {
          # 只有一个文件在这个母离子组中，放入Different_MZ
          copy_file_to_dir(mz_files$file_path[1], different_mz_dir, convert_to_txt)
          cat("文件", mz_files$file_name[1], "-> Different_MZ (唯一母离子组)\n")
        } else {
          # 多个文件在相同的母离子组中，在数字文件夹中创建母离子子文件夹
          mz_dir <- file.path(name_dir, paste0("MZ_", group_mz))
          dir.create(mz_dir, recursive = TRUE, showWarnings = FALSE)
          
          for (j in seq_len(nrow(mz_files))) {
            copy_file_to_dir(mz_files$file_path[j], mz_dir, convert_to_txt)
            cat("文件", mz_files$file_name[j], "-> SIM_MZ/", numeric_folder_name, "/MZ_", group_mz,
                " (原始MZ:", mz_files$mother_ion[j], ")\n")
          }
        }
      }
      
      folder_counter <- folder_counter + 1
    }
  }
  
  # 保存NAME映射文件
  if (nrow(name_mapping) > 0) {
    mapping_file <- file.path(sim_mz_dir, "folder_name_mapping.csv")
    write.csv(name_mapping, mapping_file, row.names = FALSE, fileEncoding = "UTF-8")
    cat("已创建文件夹名称映射文件:", mapping_file, "\n")
  }
  
  cat("\n文件分类完成！\n")
  cat("输出目录结构:\n")
  cat("- Different: NAME不同的文件\n")
  cat("- SIM/Different_MZ: NAME相同但母离子不同的文件\n")
  cat("- SIM/SIM_MZ/[NAME]/MZ_[母离子]: NAME和母离子都相同的文件\n")
}

# 辅助函数：提取母离子
extract_mother_ion <- function(filename) {
  # 移除文件扩展名
  name_without_ext <- tools::file_path_sans_ext(filename)
  
  # 按下划线分割
  parts <- strsplit(name_without_ext, "_")[[1]]
  
  if (length(parts) >= 3) {
    # 第二个下划线和第三个下划线之间的部分
    mother_ion_str <- parts[3]
    
    # 提取数字
    numbers <- str_extract_all(mother_ion_str, "\\d+\\.?\\d*")[[1]]
    if (length(numbers) > 0) {
      return(numbers[1])
    }
  }
  
  # 如果无法提取，返回"unknown"
  return("unknown")
}

# 辅助函数：根据误差容忍度对母离子进行分组
group_mother_ions_by_tolerance <- function(file_info, tolerance = 0.005) {
  # 转换母离子为数值，过滤掉无法转换的
  file_info$mz_numeric <- suppressWarnings(as.numeric(file_info$mother_ion))
  valid_files <- file_info[!is.na(file_info$mz_numeric), ]
  invalid_files <- file_info[is.na(file_info$mz_numeric), ]
  
  if (nrow(valid_files) == 0) {
    return(list(groups = list(), invalid = invalid_files))
  }
  
  # 按数值排序
  valid_files <- valid_files[order(valid_files$mz_numeric), ]
  
  # 分组算法
  groups <- list()
  group_counter <- 1
  
  for (i in seq_len(nrow(valid_files))) {
    current_mz <- valid_files$mz_numeric[i]
    assigned <- FALSE
    
    # 检查是否可以加入现有组
    for (j in seq_along(groups)) {
      group_mzs <- groups[[j]]$mz_numeric
      
      # 检查与组内任意一个母离子的差值是否在容忍度内
      if (any(abs(current_mz - group_mzs) <= tolerance)) {
        groups[[j]] <- rbind(groups[[j]], valid_files[i, ])
        assigned <- TRUE
        break
      }
    }
    
    # 如果没有合适的组，创建新组
    if (!assigned) {
      groups[[group_counter]] <- valid_files[i, ]
      group_counter <- group_counter + 1
    }
  }
  
  # 为每个组分配代表性的母离子值（使用组内平均值）
  for (i in seq_along(groups)) {
    avg_mz <- mean(groups[[i]]$mz_numeric)
    groups[[i]]$group_mz <- round(avg_mz, 3)
  }
  
  return(list(groups = groups, invalid = invalid_files))
}

# 辅助函数：清理文件名用作文件夹名
sanitize_filename <- function(name) {
  # 移除或替换不允许在文件夹名中使用的字符
  name <- gsub("[<>:\"/\\|?*]", "_", name)
  name <- gsub("\\s+", "_", name)  # 将空格替换为下划线
  return(name)
}

# 辅助函数：复制文件到指定目录（支持转换为TXT）
copy_file_to_dir <- function(source_file, dest_dir, convert_to_txt = FALSE) {
  if (!dir.exists(dest_dir)) {
    dir.create(dest_dir, recursive = TRUE, showWarnings = FALSE)
  }
  
  if (convert_to_txt) {
    # 转换为TXT格式（只包含Sheet1，不包含Sheet2）
    return(convert_excel_to_txt(source_file, dest_dir))
  } else {
    # 直接复制Excel文件
    dest_file <- file.path(dest_dir, basename(source_file))
    
    # 如果目标文件已存在，添加数字后缀
    counter <- 1
    original_dest <- dest_file
    while (file.exists(dest_file)) {
      name_part <- tools::file_path_sans_ext(basename(original_dest))
      ext_part <- tools::file_ext(basename(original_dest))
      dest_file <- file.path(dest_dir, paste0(name_part, "_", counter, ".", ext_part))
      counter <- counter + 1
    }
    
    cat("  📋 复制Excel文件:", basename(dest_file), "\n")
    return(file.copy(source_file, dest_file))
  }
}

# 辅助函数：将Excel文件转换为TXT格式（只转换第一个sheet）
convert_excel_to_txt <- function(source_file, dest_dir) {
  tryCatch({
    # 只读取Excel文件的第一个sheet（不包含sheet2的内容）
    cat("  🔄 正在转换:", basename(source_file), "-> TXT\n")
    sheet1_data <- read_excel(source_file, sheet = 1)
    
    # 生成TXT文件名
    base_name <- tools::file_path_sans_ext(basename(source_file))
    txt_file <- file.path(dest_dir, paste0(base_name, ".txt"))
    
    # 如果目标文件已存在，添加数字后缀
    counter <- 1
    original_txt <- txt_file
    while (file.exists(txt_file)) {
      txt_file <- file.path(dest_dir, paste0(base_name, "_", counter, ".txt"))
      counter <- counter + 1
    }
    
    # 写入TXT文件（制表符分隔，只包含sheet1的数据）
    write.table(sheet1_data, txt_file,
                sep = "\t",
                row.names = FALSE,
                col.names = TRUE,
                quote = FALSE,
                fileEncoding = "UTF-8",
                na = "")
    
    cat("  ✅ 成功转换为TXT:", basename(txt_file), "(只包含Sheet1数据)\n")
    return(TRUE)
    
  }, error = function(e) {
    cat("  ❌ 转换TXT失败:", basename(source_file), "-", e$message, "\n")
    return(FALSE)
  })
}
